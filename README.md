# Vue Admin Template

> 这是一个基于 vue-admin-template 的后台管理系统模板，它包含了多标签页、动态路由、权限验证等功能。

[预览地址](http://panjiachen.github.io/vue-admin-template)

## 功能特性

- 登录 / 注销
- 权限验证
  - 页面权限
  - 指令权限
  - 权限配置
  - 二步登录
- 多环境发布
  - Dev / Stage / Prod
- 全局功能
  - 动态侧边栏（支持多级路由嵌套）
  - 动态面包屑
  - 快捷导航(标签页)
  - Svg 图标
  - 本地/后端 mock 数据
  - Screenfull全屏
  - 自适应收缩侧边栏
- 错误页面
  - 401
  - 404

## 开发

```bash
# 克隆项目
git clone https://github.com/your-username/vue-admin-template.git

# 进入项目目录
cd vue-admin-template

# 安装依赖
npm install

# 建议不要直接使用 cnpm 安装以来，会有各种诡异的 bug。可以通过如下操作解决 npm 下载速度慢的问题
npm install --registry=https://registry.npm.taobao.org

# 启动服务
npm run dev
```

浏览器访问 [http://localhost:9528](http://localhost:9528)

## 发布

```bash
# 构建测试环境
npm run build:stage

# 构建生产环境
npm run build:prod
```

## 其它

```bash
# 预览发布环境效果
npm run preview

# 预览发布环境效果 + 静态资源分析
npm run preview -- --report

# 代码格式检查
npm run lint

# 代码格式检查并自动修复
npm run lint -- --fix
```

## 权限控制

### 页面权限

通过路由的 meta.roles 属性来控制页面访问权限：

```js
// 只有 admin 角色可以访问
{
  path: '/admin',
  component: Layout,
  meta: { roles: ['admin'] }
}

// admin 和 editor 都可以访问
{
  path: '/article',
  component: Layout,
  meta: { roles: ['admin', 'editor'] }
}
```

### 按钮权限

使用 v-permission 指令来控制按钮的显示权限：

```html
<!-- 只有 admin 角色可以看到 -->
<el-button v-permission="['admin']">删除</el-button>

<!-- admin 和 editor 角色都可以看到 -->
<el-button v-permission="['admin', 'editor']">编辑</el-button>
```

## 登录账号

```
管理员账号: admin
管理员密码: 111111

编辑账号: editor
编辑密码: 111111
```

## 目录结构

```bash
├── build                      # 构建相关
├── mock                       # 项目mock 模拟数据
├── public                     # 静态资源
│   │── favicon.ico           # favicon图标
│   └── index.html            # html模板
├── src                       # 源代码
│   ├── api                   # 所有请求
│   ├── assets                # 主题 字体等静态资源
│   ├── components            # 全局公用组件
│   ├── directive             # 全局指令
│   ├── filters               # 全局 filter
│   ├── icons                 # 项目所有 svg icons
│   ├── layout                # 全局 layout
│   ├── router                # 路由
│   ├── store                 # 全局 store管理
│   ├── styles                # 全局样式
│   ├── utils                 # 全局公用方法
│   ├── views                 # views 所有页面
│   ├── App.vue              # 入口页面
│   ├── main.js              # 入口文件 加载组件 初始化等
│   └── permission.js        # 权限管理
├── tests                     # 测试
├── .env.xxx                  # 环境变量配置
├── .eslintrc.js             # eslint 配置项
├── .babelrc                  # babel-loader 配置
├── .travis.yml              # 自动化CI配置
├── vue.config.js            # vue-cli 配置
├── postcss.config.js        # postcss 配置
└── package.json             # package.json
```

## Browsers support

Modern browsers and Internet Explorer 10+.

| [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/edge/edge_48x48.png" alt="IE / Edge" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>IE / Edge | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/firefox/firefox_48x48.png" alt="Firefox" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Firefox | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/chrome/chrome_48x48.png" alt="Chrome" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Chrome | [<img src="https://raw.githubusercontent.com/alrra/browser-logos/master/src/safari/safari_48x48.png" alt="Safari" width="24px" height="24px" />](http://godban.github.io/browsers-support-badges/)</br>Safari |
| --------- | --------- | --------- | --------- |
| IE10, IE11, Edge| last 2 versions| last 2 versions| last 2 versions

## License

[MIT](https://github.com/PanJiaChen/vue-admin-template/blob/master/LICENSE) license.

 