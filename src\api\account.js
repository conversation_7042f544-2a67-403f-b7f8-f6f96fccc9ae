import request from '@/utils/request'

// 获取账号列表
export function getAccountList(params) {
  return request({
    url: '/account/list',
    method: 'get',
    params
  })
}

// 创建账号
export function createAccount(data) {
  return request({
    url: '/account/add',
    method: 'post',
    data
  })
}

// 删除账号
export function deleteAccount(id) {
  return request({
    url: '/account/delete',
    method: 'post',
    data: { id }
  })
}

// 更新账号
export function updateAccount(data) {
  return request({
    url: '/account/update',
    method: 'post',
    data
  })
}

// 更新账号状态
export function updateAccountStatus(data) {
  return request({
    url: '/account/status',
    method: 'post',
    data
  })
}

// 获取账号详情
export function getAccountDetail(id) {
  return request({
    url: '/account/detail',
    method: 'get',
    params: { id }
  })
}

// 导出账号列表
export function exportAccountList(params) {
  return request({
    url: '/account/export',
    method: 'get',
    params
  })
}
