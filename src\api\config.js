import request from '@/utils/request'

/**
 * 获取轮询模式状态
 * @returns {Promise}
 */
export function getPollingStatus() {
  return request({
    url: '/common/getPollingStatus',
    method: 'get'
  })
}

/**
 * 设置轮询模式
 * @param {Object} data 轮询模式配置
 * @param {Boolean} data.enabled 是否开启轮询模式
 * @returns {Promise}
 */
export function setPollingMode(data) {
  return request({
    url: '/common/setPollingMode',
    method: 'post',
    data
  })
}

/**
 * 删除N天前的订单数据
 * @param {Object} data 删除配置
 * @param {Number} data.days 删除多少天前的订单数据
 * @param {Boolean} data.confirm 确认删除操作
 * @returns {Promise}
 */
export function deleteOldOrders(data) {
  return request({
    url: '/common/deleteOldOrders',
    method: 'post',
    data
  })
}
