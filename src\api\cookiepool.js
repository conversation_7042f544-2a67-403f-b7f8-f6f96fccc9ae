import request from '@/utils/request'

// 获取账号列表
export function getCookiepoolList(params) {
  return request({
    url: '/cookiepool/list',
    method: 'get',
    params
  })
}

// 创建账号
export function createCookiepool(data) {
  return request({
    url: '/cookiepool/add',
    method: 'post',
    data
  })
}

// 删除账号
export function deleteCookiepool(id) {
  return request({
    url: '/cookiepool/delete',
    method: 'post',
    data: { id }
  })
}

// 更新账号
export function updateCookiepool(data) {
  return request({
    url: '/cookiepool/update',
    method: 'post',
    data
  })
}

// 更新账号状态
export function updateCookiepoolStatus(data) {
  return request({
    url: '/cookiepool/status',
    method: 'post',
    data
  })
}

// 获取账号详情
export function getCookiepoolDetail(id) {
  return request({
    url: '/cookiepool/detail',
    method: 'get',
    params: { id }
  })
}
