import request from '@/utils/request'

// 获取仪表盘概览数据
export function getDashboardOverview() {
  return request({
    url: '/dashboard/overview',
    method: 'get'
  })
}

// 获取支付宝仪表盘概览数据
export function getDashboardOverviewAli() {
  return request({
    url: '/dashboard/overview_ali',
    method: 'get'
  })
}

// 获取支付通道数据
export function getChannelData(params) {
  return request({
    url: '/dashboard/channels_new',
    method: 'get',
    params
  })
}

// 获取最近一小时成功率（5分钟前的一小时）
export function getRecentHourSuccessRate(params) {
  return request({
    url: '/dashboard/recent_hour_success_rate',
    method: 'get',
    params
  })
}

// 获取最近5分钟订单量
export function getRecentFiveMinuteOrderCount(params) {
  return request({
    url: '/dashboard/recent_five_minute_order_count',
    method: 'get',
    params
  })
}

// 获取月度收款数据
export function getMonthlyRevenueData(params) {
  return request({
    url: '/dashboard/monthly_revenue',
    method: 'get',
    params
  })
}

// 获取支付宝月度收款数据
export function getMonthlyRevenueDataAli(params) {
  return request({
    url: '/dashboard/monthly_revenue_ali',
    method: 'get',
    params
  })
}
