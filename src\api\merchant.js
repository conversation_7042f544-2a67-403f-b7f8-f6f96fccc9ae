import request from '@/utils/request'

/**
 * 获取商户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getMerchantList(params) {
  return request({
    url: '/merchant/list',
    method: 'get',
    params
  })
}

/**
 * 添加商户
 * @param {Object} data - 商户数据
 * @returns {Promise}
 */
export function addMerchant(data) {
  return request({
    url: '/merchant/add',
    method: 'post',
    data
  })
}

/**
 * 更新商户信息
 * @param {Object} data - 商户数据
 * @returns {Promise}
 */
export function updateMerchant(data) {
  return request({
    url: '/merchant/update',
    method: 'post',
    data
  })
}

/**
 * 删除商户
 * @param {Object} data - 包含商户ID的对象
 * @returns {Promise}
 */
export function deleteMerchant(data) {
  return request({
    url: '/merchant/delete',
    method: 'post',
    data
  })
}

/**
 * 更新商户状态
 * @param {Object} data - 包含商户ID和状态的对象
 * @returns {Promise}
 */
export function updateMerchantStatus(data) {
  return request({
    url: '/merchant/status',
    method: 'post',
    data
  })
}

/**
 * 更新商户余额
 * @param {Object} data - 包含商户ID、金额和备注的对象
 * @returns {Promise}
 */
export function updateMerchantBalance(data) {
  return request({
    url: '/merchant/balance',
    method: 'post',
    data
  })
}

/**
 * 设置商户费率
 * @param {Object} data - 包含id, rate, time
 * @returns {Promise}
 */
export function setRate(data) {
  return request({
    url: '/merchant/setRate',
    method: 'post',
    data
  })
}

/**
 * 获取商户详情
 * @param {Object} params - {id}
 * @returns {Promise}
 */
export function getMerchantDetail(params) {
  return request({
    url: '/merchant/detail',
    method: 'get',
    params
  })
}

/**
 * 调整商户预付
 * @param {Object} data - 包含merchant_id, amount, remark的对象
 * @returns {Promise}
 */
export function adjustPrepaid(data) {
  return request({
    url: '/merchant/adjustPrepaid',
    method: 'post',
    data
  })
}
