import request from '@/utils/request'

/**
 * 获取商户分组列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，默认1
 * @param {number} params.limit - 每页数量，默认10
 * @param {string} params.name - 分组名称（模糊查询）
 * @param {number} params.status - 状态（可选）
 * @returns {Promise}
 */
export function getMerchantGroupList(params) {
  return request({
    url: '/merchant_group/list',
    method: 'get',
    params
  })
}

/**
 * 新增商户分组
 * @param {Object} data - 分组数据
 * @param {string} data.name - 分组名称（必填，唯一）
 * @param {number} data.status - 状态，默认1
 * @param {string} data.remark - 备注（可选）
 * @param {Array|string} data.merchant_ids - 商户ID数组或逗号分隔字符串（可选）
 * @returns {Promise}
 */
export function addMerchantGroup(data) {
  return request({
    url: '/merchant_group/add',
    method: 'post',
    data
  })
}

/**
 * 更新商户分组
 * @param {Object} data - 分组数据
 * @param {number} data.id - 分组ID（必填）
 * @param {string} data.name - 分组名称（可选，唯一）
 * @param {number} data.status - 状态（可选）
 * @param {string} data.remark - 备注（可选）
 * @param {Array|string} data.merchant_ids - 商户ID数组或逗号分隔字符串（可选）
 * @returns {Promise}
 */
export function updateMerchantGroup(data) {
  return request({
    url: '/merchant_group/update',
    method: 'post',
    data
  })
}

/**
 * 删除商户分组
 * @param {Object} data - 包含分组ID的对象
 * @param {number} data.id - 分组ID（必填）
 * @returns {Promise}
 */
export function deleteMerchantGroup(data) {
  return request({
    url: '/merchant_group/delete',
    method: 'post',
    data
  })
}

/**
 * 获取商户分组详情
 * @param {Object} params - 查询参数
 * @param {number} params.id - 分组ID（必填）
 * @returns {Promise}
 */
export function getMerchantGroupDetail(params) {
  return request({
    url: '/merchant_group/detail',
    method: 'get',
    params
  })
}
