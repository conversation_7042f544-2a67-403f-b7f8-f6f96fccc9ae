import request from '@/utils/request'

/**
 * 获取订单列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getOrderList(params) {
  return request({
    url: '/order/list',
    method: 'get',
    params
  })
}

/**
 * 获取订单详情
 * @param {String} id 订单ID
 * @returns {Promise}
 */
export function getOrderDetail(id) {
  return request({
    url: `/order/detail`,
    method: 'post',
    data: { id }
  })
}

/**
 * 更新订单备注
 * @param {Object} data 订单数据
 * @returns {Promise}
 */
export function updateOrderRemark(data) {
  return request({
    url: '/order/update-remark',
    method: 'post',
    data
  })
}

/**
 * 手动更新订单状态
 * @param {Object} data 订单数据
 * @returns {Promise}
 */
export function updateOrderStatus(data) {
  return request({
    url: '/order/update-status',
    method: 'post',
    data
  })
}

/**
 * 手动触发订单回调
 * @param {String} id 订单ID
 * @returns {Promise}
 */
export function callbackOrder(id) {
  return request({
    url: `/order/callback`,
    method: 'post',
    data: { id }
  })
}

/**
 * 导出订单
 * @param {Object} params 导出参数
 * @returns {Promise}
 */
export function exportOrder(params) {
  return request({
    url: '/order/export',
    method: 'get',
    params
  })
}
