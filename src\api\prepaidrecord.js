import request from '@/utils/request'

/**
 * 获取预付记录列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getPrepaidRecordList(params) {
  return request({
    url: '/prepaidrecord/list',
    method: 'get',
    params
  })
}

/**
 * 修改预付记录备注
 * @param {Object} data - 包含id和remark的对象
 * @returns {Promise}
 */
export function updatePrepaidRecordRemark(data) {
  return request({
    url: '/prepaidrecord/updateRemark',
    method: 'post',
    data
  })
}
