<template>
  <div class="navbar">
    <hamburger :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb class="breadcrumb-container" />

    <div class="right-menu">
      <el-dropdown class="avatar-container" trigger="click">
        <div class="username-wrapper">
          <span class="user-name">{{ name }}</span>
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown" class="user-dropdown">
          <el-dropdown-item>
            <router-link to="/profile">个人资料</router-link>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span style="display:block;">退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import Hamburger from '@/components/Hamburger'

export default {
  components: {
    Breadcrumb,
    Hamburger
  },
  computed: {
    ...mapGetters([
      'sidebar',
      'name'
    ])
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      await this.$store.dispatch('user/logout')
      this.$router.push(`/login?redirect=${this.$route.fullPath}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 48px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;
    padding: 0 15px;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
    padding-left: 15px;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 48px;
    margin-right: 16px;

    .avatar-container {
      .username-wrapper {
        cursor: pointer;
        padding: 0 12px;
        transition: all 0.3s;
        display: flex;
        align-items: center;

        .user-name {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          margin-right: 8px;
        }

        .el-icon-caret-bottom {
          font-size: 12px;
          color: rgba(0, 0, 0, 0.3);
        }

        &:hover {
          background: rgba(0, 0, 0, .025);
        }
      }

      .user-dropdown {
        .el-dropdown-menu__item {
          padding: 8px 16px;
          font-size: 14px;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
