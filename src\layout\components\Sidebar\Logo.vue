<template>
  <div class="sidebar-logo-container" :class="{'collapse':collapse}">
    <transition name="sidebarLogoFade">
      <div key="logo" class="sidebar-logo-link">
        <div class="logo-wrapper">
          <svg class="sidebar-logo" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <!-- 盾牌主体 -->
            <path d="M512 64L896 192v288c0 259.2-172.8 428.8-384 480-211.2-51.2-384-220.8-384-480V192L512 64z" fill="#1890ff"/>

            <!-- 渐变定义 -->
            <defs>
              <linearGradient id="metallic" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.6"/>
                <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0"/>
                <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.2"/>
              </linearGradient>
              <linearGradient id="centerGlow" x1="50%" y1="0%" x2="50%" y2="100%">
                <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8"/>
                <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.3"/>
                <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0"/>
              </linearGradient>
              <radialGradient id="innerShine" cx="50%" cy="40%" r="60%">
                <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.6"/>
                <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0"/>
              </radialGradient>
            </defs>

            <!-- 金属光泽效果 -->
            <path d="M512 64L896 192v288c0 259.2-172.8 428.8-384 480-211.2-51.2-384-220.8-384-480V192L512 64z" fill="url(#metallic)"/>

            <!-- 内部装饰线条 -->
            <path d="M512 128L832 230.4v249.6c0 224-149.3 371.2-320 416-170.7-44.8-320-192-320-416V230.4L512 128z" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.3"/>

            <!-- 放射状光晕 -->
            <circle cx="512" cy="384" r="160" fill="url(#innerShine)" opacity="0.4"/>

            <!-- 中心装饰图案 -->
            <path d="M512 320l128 96-48 160-80 48-80-48-48-160 128-96z" fill="#ffffff" opacity="0.15"/>
            <path d="M512 352l96 64-32 128-64 32-64-32-32-128 96-64z" fill="#ffffff" opacity="0.3"/>
            <path d="M512 384l64 32-16 96-48 16-48-16-16-96 64-32z" fill="url(#centerGlow)"/>

            <!-- 装饰性圆环 -->
            <circle cx="512" cy="448" r="96" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.2"/>
            <circle cx="512" cy="448" r="64" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>

            <!-- 高光效果 -->
            <path d="M512 96L832 208v48L512 144 192 256v-48L512 96z" fill="#ffffff" opacity="0.4"/>

            <!-- 边缘高光 -->
            <path d="M512 64L896 192 512 320 128 192 512 64z" fill="none" stroke="#ffffff" stroke-width="4" opacity="0.5"/>

            <!-- 中心点缀 -->
            <circle cx="512" cy="448" r="8" fill="#ffffff" opacity="0.8"/>
            <circle cx="512" cy="448" r="4" fill="#ffffff"/>

            <!-- 四角装饰 -->
            <path d="M472 408l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
            <path d="M552 408l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
            <path d="M472 488l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
            <path d="M552 488l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
          </svg>
          <h1 v-if="!collapse" class="sidebar-title">{{ title }}</h1>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import settings from '@/settings'

export default {
  name: 'SidebarLogo',
  props: {
    collapse: {
      type: Boolean,
      required: true
    }
  },
  data() {
    return {
      title: settings.getTitle()
    }
  },
  created() {
    // 监听配置变化
    if (typeof window !== 'undefined') {
      window.addEventListener('load', () => {
        this.title = settings.getTitle()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active,
.sidebarLogoFade-leave-active {
  transition: all 0.5s;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.sidebarLogoFade-enter {
  transform: rotateY(-180deg);
}

.sidebarLogoFade-leave-to {
  transform: rotateY(180deg);
}

.sidebar-logo-container {
  position: relative;
  width: 100%;
  height: 83px;
  background: #002140;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;
    padding: 0 24px;
    box-sizing: border-box;
    display: flex;
    align-items: center;

    .logo-wrapper {
      display: flex;
      align-items: center;

      & .sidebar-logo {
        width: 40px;
        height: 40px;
        margin-right: 16px;
        transition: all 0.3s ease;
      }

      & .sidebar-title {
        display: inline-block;
        margin: 0;
        color: white;
        font-weight: 600;
        font-size: 18px;
        font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
        transition: all 0.3s ease;
        line-height: 40px;
      }
    }
  }

  &.collapse {
    .sidebar-logo-link {
      padding: 0;
      justify-content: center;

      .logo-wrapper {
        justify-content: center;

        .sidebar-logo {
          margin-right: 0;
          width: 32px;
          height: 32px;
        }
      }
    }
  }
}
</style>
