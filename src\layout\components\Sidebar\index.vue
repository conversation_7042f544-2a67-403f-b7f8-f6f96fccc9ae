<template>
  <div :class="{'has-logo':showLogo}">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item v-for="route in permission_routes" :key="route.path" :item="route" :base-path="route.path" :is-collapse="isCollapse" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'

export default {
  components: { SidebarItem, Logo },
  computed: {
    ...mapGetters([
      'sidebar',
      'permission_routes'
    ]),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    showLogo() {
      return true
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  }
}
</script>

<style lang="scss" scoped>
.el-scrollbar {
  background: #001529;
  height: 100vh;

  ::v-deep {
    .scrollbar-wrapper {
      overflow-x: hidden !important;

      .el-scrollbar__view {
        height: 100%;
      }
    }
  }
}

.el-menu {
  border: none;
  height: 100%;
  width: 100% !important;
}

.has-logo {
  .el-scrollbar {
    height: calc(100vh - 83px);
  }
}

::v-deep .el-menu-item {
  height: 48px;
  line-height: 48px;
  font-size: 14px;
  padding: 0 20px 0 28px !important;
  color: rgba(255, 255, 255, 0.65);
  margin: 4px 0;

  &:hover {
    color: #fff;
    background: #1890ff !important;
  }

  &.is-active {
    color: #fff;
    background: #1890ff !important;
  }

  .el-tooltip {
    padding: 0 !important;
  }
}

::v-deep .el-submenu {
  .el-submenu__title {
    height: 48px;
    line-height: 48px;
    font-size: 14px;
    padding: 0 20px 0 28px !important;
    color: rgba(255, 255, 255, 0.65);
    margin: 4px 0;

    &:hover {
      color: #fff;
      background: #001529 !important;
    }

    i {
      color: inherit;
    }
  }

  &.is-active {
    > .el-submenu__title {
      color: #fff !important;
    }
  }

  .el-menu {
    background: #000c17 !important;
    padding: 4px 0;

    .el-menu-item {
      background: #000c17;
      padding-left: 52px !important;
      min-width: auto;
      margin: 4px 0;

      &:hover {
        color: #fff;
        background: #1890ff !important;
      }

      &.is-active {
        background: #1890ff !important;
      }
    }
  }
}

// 折叠时的样式
.el-menu--collapse {
  ::v-deep .el-submenu {
    &.is-active {
      .el-submenu__title {
        background: #1890ff !important;
      }
    }
  }
}

// 弹出菜单样式
::v-deep .el-menu--popup {
  background: #001529 !important;
  padding: 4px 0;
  min-width: 160px;

  .el-menu-item {
    height: 48px;
    line-height: 48px;
    padding-left: 28px !important;
    color: rgba(255, 255, 255, 0.65);
    background: #001529;
    margin: 4px 0;

    &:hover {
      color: #fff;
      background: #1890ff !important;
    }

    &.is-active {
      color: #fff;
      background: #1890ff !important;
    }
  }
}
</style>
