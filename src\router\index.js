import Vue from 'vue'
import Router from 'vue-router'

Vue.use(Router)

/* Layout */
import Layout from '@/layout'

/**
 * 注意: 子菜单只在路由children.length >= 1时出现
 * 详情参见: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   当设置 true 的时候该路由不会在侧边栏出现 如401，login等页面(默认 false)
 * alwaysShow: true               当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
 *                                只有一个时，会将那个子路由当做根路由显示在侧边栏，
 *                                若你想不管路由下面的 children 声明的个数都显示你的根路由，
 *                                你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
 *                                一直显示根路由
 * redirect: noRedirect           当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'             设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * meta : {
    roles: ['admin','editor']     设置该路由进入的权限，支持多个权限叠加
    title: 'title'                设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'/'el-icon-x'  设置该路由的图标，支持 svg-class，也支持 el-icon-x element-ui 的 icon
    noCache: true                 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    affix: true                   如果设置为true，则会一直固定在tags-view中(默认 false)
    breadcrumb: false             如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)
    activeMenu: '/example/list'   当路由设置了该属性，则会高亮相对应的侧边栏，
                                 这在某些场景非常有用，比如：一个文章的列表页路由为：/article/list
                                 点击文章进入文章详情页，这时候路由为/article/1，但你想在侧边栏高亮文章列表的路由，
                                 就可以进行如下设置
  }
 */

/**
 * constantRoutes
 * 不需要动态判断权限的路由
 * 所有角色都可以访问
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index'),
        name: 'Redirect',
        meta: { title: 'Redirect', noCache: true }
      }
    ]
  },

  {
    path: '/login',
    component: () => import('@/views/login/index'),
    hidden: true
  },

  {
    path: '/404',
    component: () => import('@/views/404'),
    hidden: true
  },

  {
    path: '/profile',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '',
        name: 'Profile',
        component: () => import('@/views/profile/index'),
        meta: { title: '个人资料' }
      }
    ]
  },

  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [{
      path: 'dashboard',
      name: 'Dashboard',
      component: () => import('@/views/dashboard/index'),
      meta: { title: '仪表盘', icon: 'el-icon-monitor', affix: true }
    }]
  }
]

/**
 * asyncRoutes
 * 需要根据用户角色动态加载的路由
 */
export const asyncRoutes = [
  {
    path: '/system',
    component: Layout,
    redirect: '/system/user',
    name: 'System',
    meta: { title: '系统管理', icon: 'el-icon-setting', roles: ['超管'] },
    children: [
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/user/index'),
        meta: { title: '用户管理', icon: 'el-icon-user', roles: ['超管'] }
      },
      {
        path: 'config',
        name: 'Config',
        component: () => import('@/views/config/index'),
        meta: { title: '配置管理', icon: 'el-icon-setting', roles: ['超管'] }
      }
      // {
      //   path: 'cookiepool',
      //   name: 'CookiePool',
      //   component: () => import('@/views/cookiepool/index'),
      //   meta: { title: 'Cookie池', icon: 'el-icon-user-solid', roles: ['超管'] }
      // }
      // {
      //   path: 'role',
      //   name: 'Role',
      //   component: () => import('@/views/role/index'),
      //   meta: { title: '角色管理', icon: 'el-icon-user-solid', roles: ['超管', '一级服务商'] }
      // }
    ]
  },
  {
    path: '/account',
    component: Layout,
    redirect: '/account/manager',
    name: 'Account',
    meta: { title: '账号管理', icon: 'el-icon-user', roles: ['超管', '服务商'] },
    children: [
      {
        path: 'manager',
        name: 'AccountManager',
        component: () => import('@/views/account/index'),
        meta: { title: '账号管理', icon: 'el-icon-user' }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order/manager',
    name: 'Order',
    meta: { title: '订单管理', icon: 'el-icon-shopping-cart-full', roles: ['超管', '服务商'] },
    children: [
      {
        path: 'manager',
        name: 'OrderManager',
        component: () => import('@/views/order/index'),
        meta: { title: '订单管理', icon: 'el-icon-shopping-cart-full' }
      }
    ]
  },
  {
    path: '/merchant',
    component: Layout,
    redirect: '/merchant/manager',
    name: 'Merchant',
    meta: { title: '商户管理', icon: 'el-icon-link', roles: ['超管', '服务商'] },
    children: [
      {
        path: 'manager',
        name: 'MerchantManager',
        component: () => import('@/views/merchant/index'),
        meta: { title: '商户管理', icon: 'el-icon-link' }
      },
      {
        path: 'group',
        name: 'MerchantGroup',
        component: () => import('@/views/merchant_group/index'),
        meta: { title: '分组管理', icon: 'el-icon-collection' }
      }
    ]
  },
  {
    path: '/prepaidrecord',
    component: Layout,
    redirect: '/prepaidrecord/list',
    name: 'PrepaidRecord',
    meta: { title: '预付记录', icon: 'el-icon-document', roles: ['超管', '服务商'] },
    children: [
      {
        path: 'list',
        name: 'PrepaidRecordList',
        component: () => import('@/views/prepaidrecord/index'),
        meta: { title: '预付记录', icon: 'el-icon-document' }
      }
    ]
  },

  // {
  //   path: '/example',
  //   component: Layout,
  //   redirect: '/example/table',
  //   name: 'Example',
  //   meta: { title: '示例', icon: 'el-icon-s-help', roles: ['admin', 'editor'] },
  //   children: [
  //     {
  //       path: 'table',
  //       name: 'Table',
  //       component: () => import('@/views/table/index'),
  //       meta: { title: '表格', icon: 'el-icon-s-grid' }
  //     },
  //     {
  //       path: 'tree',
  //       name: 'Tree',
  //       component: () => import('@/views/tree/index'),
  //       meta: { title: '树形控件', icon: 'el-icon-share', roles: ['admin'] }
  //     }
  //   ]
  // },

  // {
  //   path: '/form',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'index',
  //       name: 'Form',
  //       component: () => import('@/views/form/index'),
  //       meta: { title: '表单', icon: 'el-icon-s-order', roles: ['admin', 'editor'] }
  //     }
  //   ]
  // },

  // {
  //   path: '/nested',
  //   component: Layout,
  //   redirect: '/nested/menu1',
  //   name: 'Nested',
  //   meta: {
  //     title: '嵌套菜单',
  //     icon: 'el-icon-menu',
  //     roles: ['admin']
  //   },
  //   children: [
  //     {
  //       path: 'menu1',
  //       component: () => import('@/views/nested/menu1/index'),
  //       name: 'Menu1',
  //       meta: { title: '菜单1', icon: 'el-icon-s-fold' },
  //       children: [
  //         {
  //           path: 'menu1-1',
  //           component: () => import('@/views/nested/menu1/menu1-1'),
  //           name: 'Menu1-1',
  //           meta: { title: '菜单1-1', icon: 'el-icon-s-operation' }
  //         },
  //         {
  //           path: 'menu1-2',
  //           component: () => import('@/views/nested/menu1/menu1-2'),
  //           name: 'Menu1-2',
  //           meta: { title: '菜单1-2', icon: 'el-icon-s-operation' },
  //           children: [
  //             {
  //               path: 'menu1-2-1',
  //               component: () => import('@/views/nested/menu1/menu1-2/menu1-2-1'),
  //               name: 'Menu1-2-1',
  //               meta: { title: '菜单1-2-1', icon: 'el-icon-s-operation' }
  //             },
  //             {
  //               path: 'menu1-2-2',
  //               component: () => import('@/views/nested/menu1/menu1-2/menu1-2-2'),
  //               name: 'Menu1-2-2',
  //               meta: { title: '菜单1-2-2', icon: 'el-icon-s-operation' }
  //             }
  //           ]
  //         },
  //         {
  //           path: 'menu1-3',
  //           component: () => import('@/views/nested/menu1/menu1-3'),
  //           name: 'Menu1-3',
  //           meta: { title: '菜单1-3', icon: 'el-icon-s-operation' }
  //         }
  //       ]
  //     },
  //     {
  //       path: 'menu2',
  //       component: () => import('@/views/nested/menu2/index'),
  //       name: 'Menu2',
  //       meta: { title: '菜单2', icon: 'el-icon-s-fold' }
  //     }
  //   ]
  // },

  // {
  //   path: 'external-link',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'https://panjiachen.github.io/vue-element-admin-site/#/',
  //       meta: { title: '外部链接', icon: 'el-icon-link', roles: ['admin', 'editor'] }
  //     }
  //   ]
  // },

  // 404 页面必须放在末尾 !!!
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes
})

const router = createRouter()

export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
