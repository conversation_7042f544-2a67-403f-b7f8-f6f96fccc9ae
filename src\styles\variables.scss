// sidebar
$menuText: rgba(255, 255, 255, 0.65);
$menuActiveText: #fff;
$subMenuActiveText: #fff;

$menuBg: #001529;
$menuHover: #1890ff;

$subMenuBg: #000c17;
$subMenuHover: #1890ff;

$sideBarWidth: 256px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
