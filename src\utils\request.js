import axios from 'axios'
import { MessageBox, Message } from 'element-ui'
import store from '@/store'
import { getToken } from '@/utils/auth'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = 基础url + 请求url
  // withCredentials: true, // 跨域请求时是否需要使用凭证
  // timeout: 5000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么

    if (store.getters.token) {
      // 让每个请求携带自定义token
      // ['X-Token']是自定义的请求头key
      // 根据实际情况自行修改
      config.headers['X-Token'] = getToken()
    }
    return config
  },
  error => {
    // 处理请求错误
    console.log(error) // 用于调试
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  /**
   * 如果你想获取http信息，如headers或status
   * 请返回  response => response
  */

  /**
   * 通过自定义code判断请求状态
   * 这里只是一个例子
   * 你也可以通过HTTP状态码判断状态
   */
  response => {
    const res = response.data

    // 如果返回的状态码不是1，说明接口异常，应该提示错误信息
    if (res.code !== 1) {
      Message({
        message: res.msg || '系统异常',
        type: 'error',
        duration: 5 * 1000
      })

      // token失效，需要重新登录
      if (res.code === 401) {
        MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
          confirmButtonText: '重新登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          store.dispatch('user/resetToken').then(() => {
            location.reload()
          })
        })
      }
      return Promise.reject(new Error(res.msg || '系统异常'))
    } else {
      return res
    }
  },
  error => {
    if (error.response && error.response.status === 401) {
      MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        store.dispatch('user/resetToken').then(() => {
          location.reload()
        })
      })
    }
    Message({
      message: error.message || '系统异常',
      type: 'error',
      duration: 5 * 1000
    })
    return Promise.reject(error)
  }
)

export default service
