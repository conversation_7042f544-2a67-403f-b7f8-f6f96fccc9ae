<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 - 包含用户名输入框、状态选择器、收款类型选择器和操作按钮 -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.name"
          placeholder="收款账号"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.remark"
          placeholder="备注"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.nickname"
          placeholder="创建人"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.minDiamond"
          placeholder="最小余额"
          style="width: 100px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <span class="filter-separator">-</span>
        <el-input
          v-model="listQuery.maxDiamond"
          placeholder="最大余额"
          style="width: 100px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          class="filter-item"
          style="width: 130px"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          >
            <span :style="{ color: item.key ? '#13ce66' : '#ff4949' }">{{ item.label }}</span>
          </el-option>
        </el-select>
        <el-select
          v-model="listQuery.hasError"
          placeholder="是否异常"
          clearable
          class="filter-item"
          style="width: 130px"
        >
          <el-option
            v-for="item in errorOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          />
        </el-select>
        <el-button
          class="filter-item"
          type="primary"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          type="info"
          @click="handleReset"
        >
          重置
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="success"
          @click="handleAdd"
        >
          新增
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="warning"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出
        </el-button>
      </div>
      <!-- 常用搜索区域，放在搜索栏下方，文本超链形式 -->
      <div class="common-search-block">
        <span class="common-search-title">常用搜索</span>
        <a href="javascript:void(0)" class="common-search-link" @click="handleCommonSearch('available')">在收账号</a>
        <span class="common-search-sep">|</span>
        <a href="javascript:void(0)" class="common-search-link" @click="handleCommonSearch('unavailable')">待恢复账号</a>
        <span class="common-search-sep">|</span>
        <a href="javascript:void(0)" class="common-search-link" @click="handleCommonSearch('banned')">冻结账号</a>
      </div>

      <!-- 表格区域 - 展示账号列表数据 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        highlight-current-row
        size="mini"
        class="compact-table"
      >
        <el-table-column type="selection" width="40" align="center" />
        <el-table-column prop="id" label="ID" align="center" width="60" />
        <el-table-column prop="name" label="收款账号" min-width="100" align="center" />
        <el-table-column prop="diamond" label="号内余额" width="120" align="center">
          <template slot-scope="scope">
            <div class="diamond-container">
              <span class="diamond-amount">{{ scope.row.diamond || 0 }}</span>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-refresh"
                :loading="scope.row.refreshing"
                @click="refreshDiamond(scope.row)"
                class="refresh-btn"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="权重" width="80" align="center">
          <template slot-scope="scope">
            <span class="weight-text">{{ scope.row.weight || 0 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="收款统计" align="center">
          <el-table-column prop="todayAmount" label="今日收款" width="90" align="center">
            <template slot-scope="scope">
              <span class="today-amount">{{ formatAmount(scope.row.todayAmount) }}</span>
              <br>
              <span class="today-count">{{ scope.row.todayCount }}笔</span>
            </template>
          </el-table-column>
          <el-table-column prop="yesterdayAmount" label="昨日收款" width="90" align="center">
            <template slot-scope="scope">
              <span class="amount-text">{{ formatAmount(scope.row.yesterdayAmount) }}</span>
              <br>
              <span class="amount-text">{{ scope.row.yesterdayCount }}笔</span>
            </template>
          </el-table-column>
          <el-table-column prop="beforeAmount" label="前日收款" width="90" align="center">
            <template slot-scope="scope">
              <span class="amount-text">{{ formatAmount(scope.row.beforeAmount) }}</span>
              <br>
              <span class="amount-text">{{ scope.row.beforeCount }}笔</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="70" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="errorMsg" label="异常信息" min-width="100" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <span :class="{ 'error-msg': scope.row.errormsg }">{{ scope.row.errormsg || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="100" align="center" show-overflow-tooltip />
        <el-table-column prop="nickname" label="创建人" min-width="90" align="center" show-overflow-tooltip />
        <el-table-column prop="createTime" label="创建时间" min-width="140" align="center" class-name="time-column">
          <template slot-scope="scope">
            {{ formatTime(scope.row.createtime) }}
          </template>
        </el-table-column>
        <el-table-column prop="requestTime" label="请求时间" min-width="140" align="center" class-name="time-column">
          <template slot-scope="scope">
            {{ formatTime(scope.row.pulltime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 - 控制每页显示数量和页码切换 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          small
          class="compact-pagination"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 虎牙账号表单对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="handleDialogClose"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px"
        size="small"
      >
        <el-form-item label="登录二维码">
          <div class="qr-code-container">
            <img v-if="qrCodeUrl" :src="qrCodeUrl" class="qr-code" alt="登录二维码" />
            <el-button v-if="!qrCodeUrl" type="primary" size="small" @click="getQrCode">获取二维码</el-button>
            <el-button v-else type="primary" size="small" @click="getQrCode">刷新二维码</el-button>
          </div>
        </el-form-item>
        <el-form-item label="收款账号" prop="name">
          <el-input v-model="form.name" placeholder="请输入收款账号" />
        </el-form-item>
        <el-form-item label="权重" prop="weight">
          <el-input-number
            v-model="form.weight"
            :min="0"
            :max="100"
            :precision="0"
            placeholder="请输入权重"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="Cookie" prop="cookie">
          <el-input
            v-model="form.cookie"
            type="textarea"
            :rows="4"
            placeholder="请输入cookie"
          />
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="form.status"
            :active-value="1"
            :inactive-value="0"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入API接口
import { createAccount, getAccountList, updateAccount, updateAccountStatus, deleteAccount, getAccountDetail, exportAccountList } from '@/api/account'
import request from '@/utils/request'

export default {
  name: 'Account',
  data() {
    return {
      // 查询参数对象
      listQuery: {
        name: '', // 用户名搜索条件
        remark: '', // 备注搜索条件
        nickname: '', // 创建人搜索条件
        status: '', // 状态搜索条件
        hasError: '', // 是否有异常信息
        minDiamond: '', // 最小余额
        maxDiamond: '', // 最大余额
        page: 1, // 当前页码
        limit: 10 // 每页显示条数
      },
      // 状态选项数据
      statusOptions: [
        { key: 1, label: '开启' },
        { key: 0, label: '禁用' }
      ],
      // 异常信息选项数据
      errorOptions: [
        { key: 1, label: '是' },
        { key: 0, label: '否' }
      ],
      tableData: [], // 表格数据，从API获取
      currentPage: 1, // 当前页码
      pageSize: 10, // 每页显示条数
      total: 0, // 总记录数
      loading: false, // 表格加载状态
      dialogVisible: false, // 对话框可见性
      dialogTitle: '', // 对话框标题
      // 表单数据对象
      form: {
        name: '', // 账号名称
        status: 1, // 状态，默认开启
        remark: '', // 备注
        cookie: '', // cookie
        weight: 0 // 权重，默认0
      },
      // 表单验证规则
      rules: {
        name: [
          { required: true, message: '请输入收款账号', trigger: 'blur' }
        ],
        cookie: [
          { required: true, message: '请输入cookie', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入权重', trigger: 'blur' },
          { type: 'number', min: 0, max: 100, message: '权重必须在0-100之间', trigger: 'blur' }
        ]
      },
      qrCodeUrl: '', // 登录二维码URL
      qrId: '', // 二维码ID
      qrTimer: null, // 定时器
      exportLoading: false // 导出加载状态
    }
  },
  // 组件创建时获取列表数据
  created() {
    this.getList()
  },
  methods: {
    // 格式化时间戳为可读时间格式
    formatTime(timestamp) {
      if (!timestamp) return '-'
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    // 格式化金额为货币格式
    formatAmount(amount) {
      if (amount === undefined || amount === null) return '¥0.00'
      // 确保 amount 是数字类型
      const numAmount = parseFloat(amount)
      if (isNaN(numAmount)) return '¥0.00'
      return `¥${numAmount.toFixed(2)}`
    },
    // 获取账号列表数据
    getList() {
      this.loading = true

      // 处理hasError参数，转换为后端需要的格式
      const query = { ...this.listQuery }

      // 如果设置了hasError值，转化为后端需要的查询参数
      if (query.hasError !== '' && query.hasError !== undefined) {
        // hasError为1时，搜索有错误信息的记录
        // hasError为0时，搜索没有错误信息的记录
        query.hasErrorMsg = query.hasError === 1
      }

      // 删除前端使用的hasError字段，防止传递给后端
      delete query.hasError

      getAccountList(query).then(response => {
        const { list, total, page, limit } = response.data

        // 处理返回的数据，映射字段名
        this.tableData = list.map(item => {
          const mappedItem = {
            ...item,
            status: item.status === 1, // 将状态值转为布尔值用于开关组件
            todayAmount: item.today_amount, // 映射今日收款字段
            yesterdayAmount: item.yesterday_amount, // 映射昨日收款字段
            beforeAmount: item.before_amount, // 映射前日收款金额字段
            beforeCount: item.before_count, // 映射前日收款次数
            todayCount: item.today_count, // 映射今日收款次数
            yesterdayCount: item.yesterday_count, // 映射昨日收款次数
            diamond: item.diamond || 0, // 映射余额字段
            weight: item.weight || 0 // 映射权重字段
          }
          return mappedItem
        })

        this.total = total
        this.currentPage = page
        this.pageSize = limit
        this.loading = false
      })
    },
    // 映射后端支付类型到前端使用的格式
    mapPayType(backendPayType) {
      // 后端返回的就是 alipay_jsa，无需映射
      return backendPayType
    },
    // 映射前端支付类型到后端需要的格式
    mapPayTypeToBackend(frontendPayType) {
      // 前端的值就是后端需要的，无需转换
      return frontendPayType
    },
    // 处理搜索按钮点击事件
    handleFilter() {
      this.listQuery.page = 1 // 重置为第一页
      this.getList() // 重新获取列表数据
    },
    // 处理重置按钮点击事件
    handleReset() {
      this.resetQuery() // 重置查询参数
      this.getList() // 重新获取列表数据
    },
    // 常用搜索处理
    handleCommonSearch(type) {
      this.resetQuery() // 先清空所有条件
      if (type === 'available') {
        this.listQuery.status = 1
        this.listQuery.hasError = 0
      } else if (type === 'unavailable') {
        this.listQuery.status = ''
        this.listQuery.hasError = 1
      } else if (type === 'banned') {
        this.listQuery.status = 0
        this.listQuery.hasError = ''
      }
      this.handleFilter()
    },
    // 处理新增按钮点击事件
    handleAdd() {
      this.dialogTitle = '新增账号'
      this.dialogVisible = true // 直接显示抖音表单
      this.resetTemp() // 重置临时数据对象
    },
    // 处理编辑按钮点击事件
    handleEdit(row) {
      this.dialogTitle = '编辑账号'
      // 先获取账号详情
      getAccountDetail(row.id).then(response => {
        const detailData = response.data
        // 设置临时数据对象，使用空对象作为config的默认值
        this.form = {
          ...detailData,
          status: detailData.status === 1 ? 1 : 0,
          cookie: detailData.config?.cookie || '', // 添加 cookie 字段
          weight: detailData.weight || 0 // 添加权重字段
        }

        // 直接打开抖音表单
        this.dialogVisible = true
      })
    },
    // 重置临时数据对象
    resetTemp() {
      this.form = {
        name: '',
        status: 1,
        remark: '',
        cookie: '',
        weight: 0
      }
    },
    // 重置查询参数
    resetQuery() {
      this.listQuery = {
        name: '',
        remark: '',
        nickname: '',
        status: '',
        hasError: '',
        minDiamond: '',
        maxDiamond: '',
        page: 1,
        limit: 10
      }
    },
    // 处理删除按钮点击事件
    handleDelete(row) {
      this.$confirm('确认删除该账号吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用删除账号的API
        deleteAccount(row.id).then(response => {
          this.$message.success('删除成功')
          this.getList() // 重新获取列表数据
        })
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    // 处理每页显示条数变化事件
    handleSizeChange(val) {
      this.pageSize = val
      this.listQuery.limit = val
      this.getList()
    },
    // 处理页码变化事件
    handleCurrentChange(val) {
      this.currentPage = val
      this.listQuery.page = val
      this.getList()
    },
    // 处理表单提交事件
    handleFormSubmit(formData) {
      // 处理提交的数据，转换字段名
      const submitData = {
        ...formData
      }
      delete submitData.payType // 删除前端使用的字段

      // 根据对话框标题判断是新增还是编辑
      if (this.dialogTitle === '新增账号') {
        // 调用新增账号的API
        createAccount(submitData).then(response => {
          this.$message.success('新增成功')
          this.dialogVisible = false
          this.getList() // 重新获取列表数据
        })
      } else {
        // 调用编辑账号的API
        updateAccount(submitData).then(response => {
          this.$message.success('编辑成功')
          this.dialogVisible = false
          this.getList() // 重新获取列表数据
        })
      }
    },
    // 处理状态开关变化事件
    handleStatusChange(row) {
      const newStatus = row.status ? 1 : 0

      // 调用修改状态的API
      updateAccountStatus({ id: row.id, status: newStatus }).then(response => {
        this.$message.success(`状态已${newStatus === 1 ? '开启' : '禁用'}`)
      })
    },
    // 获取收款类型对应的标签类型
    getPayTypeTagType(payType) {
      const found = this.payTypeOptions.find(item => item.key === payType)
      return found ? found.tagType : 'info'
    },
    // 获取收款类型对应的标签文本
    getPayTypeLabel(payType) {
      const found = this.payTypeOptions.find(item => item.key === payType)
      const result = found ? found.label : payType
      return result
    },
    // 获取登录二维码
    getQrCode() {
      request({
        url: '/account/getQeid',
        method: 'get'
      }).then(response => {
        if (response.code === 1 && response.data && response.data.qrId) {
          this.qrId = response.data.qrId
          this.qrCodeUrl = `https://udblgn.huya.com/qrLgn/getQrImg?appId=5002&k=${this.qrId}`
          // 开始定时检查登录状态
          this.startQrCheck()
        } else {
          this.$message.error(response.msg || '获取二维码失败')
        }
      }).catch(error => {
        console.error('获取二维码失败:', error)
        this.$message.error('获取二维码失败')
      })
    },
    // 开始定时检查登录状态
    startQrCheck() {
      // 清除可能存在的旧定时器
      this.stopQrCheck()
      // 创建新的定时器
      this.qrTimer = setInterval(() => {
        this.checkQrLogin()
      }, 3000)
    },
    // 停止定时检查
    stopQrCheck() {
      if (this.qrTimer) {
        clearInterval(this.qrTimer)
        this.qrTimer = null
      }
    },
    // 检查登录状态
    checkQrLogin() {
      if (!this.qrId) return
      request({
        url: '/account/tryQrcode',
        method: 'get',
        params: { qrId: this.qrId }
      }).then(response => {
        if (response.code === 1 && response.data) {
          // 登录成功，自动填充表单
          this.form.name = response.data.name || ''
          this.form.cookie = response.data.cookie || ''
          // 停止定时检查
          this.stopQrCheck()
          this.$message.success('登录成功')
        }
      }).catch(error => {
        console.error('检查登录状态失败:', error)
      })
    },
    // 处理对话框关闭事件
    handleDialogClose() {
      this.resetTemp()
      this.qrCodeUrl = '' // 清空二维码URL
      this.qrId = '' // 清空二维码ID
      this.stopQrCheck() // 停止定时检查
    },
    // 处理表单提交事件
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.handleFormSubmit(this.form)
        } else {
          this.$message.error('表单验证失败')
        }
      })
    },
    // 刷新单个账号的余额
    refreshDiamond(row) {
      // 设置刷新状态
      this.$set(row, 'refreshing', true)

      // 调用刷新余额的API
      request({
        url: '/account/refreshDiamond',
        method: 'post',
        data: { id: row.id }
      }).then(response => {
        if (response.code === 1 && response.data) {
          // 更新余额数据
          this.$set(row, 'diamond', response.data.diamond || 0)
          this.$message.success('余额刷新成功')
        } else {
          this.$message.error(response.msg || '余额刷新失败')
        }
      }).catch(error => {
        console.error('刷新余额失败:', error)
        this.$message.error('余额刷新失败')
      }).finally(() => {
        // 清除刷新状态
        this.$set(row, 'refreshing', false)
      })
    },
    // 处理导出按钮点击事件
    handleExport() {
      this.$confirm('确认导出当前筛选条件下的账号列表吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.exportLoading = true

        // 处理hasError参数，转换为后端需要的格式
        const query = { ...this.listQuery }

        // 如果设置了hasError值，转化为后端需要的查询参数
        if (query.hasError !== '' && query.hasError !== undefined) {
          query.hasErrorMsg = query.hasError === 1
        }

        // 删除前端使用的hasError字段，防止传递给后端
        delete query.hasError

        // 导出时忽略分页参数
        delete query.page
        delete query.limit

        exportAccountList(query).then(response => {
          if (response.code === 1 && response.data) {
            const { download_url, filename, total_count } = response.data

            // 创建下载链接
            const link = document.createElement('a')
            link.href = download_url
            link.download = filename
            link.style.display = 'none'
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)

            this.$message.success(`导出成功，共导出 ${total_count} 条记录`)
          } else {
            this.$message.error(response.msg || '导出失败')
          }
          this.exportLoading = false
        }).catch(error => {
          console.error('导出失败:', error)
          this.$message.error('导出失败')
          this.exportLoading = false
        })
      }).catch(() => {
        this.$message.info('已取消导出')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/* 应用容器样式 */
.app-container {
  padding: 12px;
}

/* 搜索区域样式 */
.common-search-group {
  margin-bottom: 8px;
  .el-button {
    margin-right: 4px;
  }
  .el-button:last-child {
    margin-right: 0;
  }
}
.common-search-divider {
  height: 1px;
  background: #f0f0f0;
  margin-bottom: 12px;
}
.filter-container {
  padding-bottom: 12px;
  .filter-item {
    margin-right: 8px;
    vertical-align: middle;
    &:last-child {
      margin-right: 0;
    }
  }
  .filter-separator {
    margin: 0 5px;
    color: #909399;
    font-weight: bold;
  }
}

/* 分页区域样式 */
.pagination-container {
  margin-top: 12px;
  text-align: right;
}

/* 错误信息样式 */
.error-msg {
  color: #F56C6C;
}

/* 今日收款金额样式 */
.today-amount {
  color: #409EFF;
  font-weight: bold;
  font-size: 13px;
}

/* 金额文本样式 */
.amount-text {
  color: #606266;
  font-size: 13px;
}

/* 余额容器样式 */
.diamond-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

/* 余额金额样式 */
.diamond-amount {
  color: #409EFF;
  font-weight: bold;
  font-size: 13px;
}

/* 刷新按钮样式 */
.refresh-btn {
  padding: 2px;
  margin-left: 2px;

  &:hover {
    color: #409EFF;
  }
}

/* 权重文本样式 */
.weight-text {
  color: #E6A23C;
  font-weight: bold;
  font-size: 13px;
}

/* 紧凑表格样式 */
.compact-table {
  font-size: 12px;

  ::v-deep .el-table__header th {
    padding: 8px 0;
    background-color: #f5f7fa;
    font-size: 12px;
  }

  ::v-deep .el-table__body td {
    padding: 5px 0;
  }

  ::v-deep .el-tag {
    text-transform: capitalize;
    font-size: 11px;
    padding: 0 5px;
    height: 20px;
    line-height: 18px;
  }

  ::v-deep .time-column {
    font-size: 11px;
    color: #606266;
  }

  ::v-deep .el-button--mini {
    padding: 4px 7px;
    font-size: 11px;
    margin-left: 0;
    margin-right: 4px;
  }

  ::v-deep .el-button--mini:last-child {
    margin-right: 0;
  }

  ::v-deep .el-button [class^="el-icon-"] {
    font-size: 11px;
    margin-right: 2px;
  }

  ::v-deep .el-switch__core {
    width: 36px;
    height: 16px;
  }

  ::v-deep .el-switch__core:after {
    width: 12px;
    height: 12px;
    top: 1px;
  }
}

/* 紧凑分页样式 */
.compact-pagination {
  ::v-deep .el-pagination__total {
    margin-left: 10px;
  }
  ::v-deep .el-pagination__sizes {
    margin-right: 10px;
  }
  ::v-deep .el-pagination__jump {
    margin-right: 10px;
  }
}

/* 二维码容器样式 */
.qr-code-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;

  .qr-code {
    width: 100px;
    height: 100px;
    margin-right: 10px;
  }
}

/* 常用搜索块样式 */
.common-search-block {
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 14px;
  display: flex;
  align-items: center;
}
.common-search-title {
  font-weight: bold;
  color: #606266;
  font-size: 13px;
  margin-right: 12px;
}
.common-search-link {
  color: #409EFF;
  cursor: pointer;
  text-decoration: underline;
  margin: 0 4px;
}
.common-search-link:hover {
  color: #66b1ff;
}
.common-search-sep {
  color: #bbb;
  margin: 0 2px;
}
</style>
