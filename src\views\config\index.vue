<template>
  <div class="config-container">
    <el-card class="config-card">
      <div slot="header" class="card-header">
        <span>系统配置</span>
      </div>

      <!-- 轮询模式配置 -->
      <el-row :gutter="20" class="config-section">
        <el-col :span="24">
          <h3>轮询模式配置</h3>
          <el-form :model="pollingForm" label-width="120px">
            <el-form-item label="轮询模式状态">
              <el-switch
                v-model="pollingForm.enabled"
                active-text="开启"
                inactive-text="关闭"
                @change="handlePollingChange"
                :loading="pollingLoading"
              />
              <div class="form-tip">
                <i class="el-icon-info"></i>
                开启轮询模式后，系统将自动轮询订单状态
              </div>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>

      <!-- 删除订单数据配置 -->
      <el-row :gutter="20" class="config-section">
        <el-col :span="24">
          <h3>数据清理配置</h3>
          <el-form :model="deleteForm" label-width="120px">
            <el-form-item label="删除天数">
              <el-input-number
                v-model="deleteForm.days"
                :min="1"
                :max="365"
                placeholder="请输入要删除多少天前的订单数据"
                style="width: 200px"
              />
              <span class="unit-text">天前</span>
              <div class="form-tip">
                <i class="el-icon-warning"></i>
                此操作将永久删除指定天数前的订单数据，请谨慎操作
              </div>
            </el-form-item>
            <el-form-item>
              <el-button
                type="danger"
                @click="handleDeleteOrders"
                :loading="deleteLoading"
                :disabled="!deleteForm.days || deleteForm.days < 1"
              >
                删除订单数据
              </el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { getPollingStatus, setPollingMode, deleteOldOrders } from '@/api/config'

export default {
  name: 'Config',
  data() {
    return {
      // 轮询模式表单
      pollingForm: {
        enabled: false
      },
      pollingLoading: false,

      // 删除订单表单
      deleteForm: {
        days: 30
      },
      deleteLoading: false
    }
  },
  created() {
    this.getPollingStatus()
  },
  methods: {
    /**
     * 获取轮询模式状态
     */
    async getPollingStatus() {
      try {
        this.pollingLoading = true
        const response = await getPollingStatus()
        this.pollingForm.enabled = response.data.enabled
        this.$message.success('获取轮询模式状态成功')
      } catch (error) {
        console.error('获取轮询模式状态失败:', error)
        this.$message.error('获取轮询模式状态失败')
      } finally {
        this.pollingLoading = false
      }
    },

    /**
     * 处理轮询模式变更
     */
    async handlePollingChange(value) {
      try {
        this.pollingLoading = true
        const response = await setPollingMode({ enabled: value })
        this.pollingForm.enabled = response.data.enabled
        this.$message.success(value ? '轮询模式已开启' : '轮询模式已关闭')
      } catch (error) {
        console.error('设置轮询模式失败:', error)
        this.$message.error('设置轮询模式失败')
        // 恢复原状态
        this.pollingForm.enabled = !value
      } finally {
        this.pollingLoading = false
      }
    },

    /**
     * 处理删除订单数据
     */
    async handleDeleteOrders() {
      if (!this.deleteForm.days || this.deleteForm.days < 1) {
        this.$message.warning('请输入有效的删除天数')
        return
      }

      // 确认删除操作
      try {
        await this.$confirm(
          `确定要删除${this.deleteForm.days}天前的订单数据吗？此操作不可恢复！`,
          '确认删除',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        this.deleteLoading = true
        const response = await deleteOldOrders({
          days: this.deleteForm.days,
          confirm: true
        })

        this.$message.success(
          `成功删除${response.data.deleted_count}条${this.deleteForm.days}天前的订单数据`
        )
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除订单数据失败:', error)
          this.$message.error('删除订单数据失败')
        }
      } finally {
        this.deleteLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  padding: 20px;

  .config-card {
    .card-header {
      font-size: 18px;
      font-weight: bold;
    }

    .config-section {
      margin-bottom: 30px;

      h3 {
        margin-bottom: 20px;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-left: 4px solid #409EFF;
        padding-left: 10px;
      }

      .form-tip {
        margin-top: 8px;
        font-size: 12px;
        color: #909399;

        i {
          margin-right: 4px;
        }
      }

      .unit-text {
        margin-left: 8px;
        color: #606266;
      }
    }
  }
}
</style>
