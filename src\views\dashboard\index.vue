<template>
  <div class="dashboard-container">
    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="data-overview" v-loading="loading.overview">
      <el-col :span="24" class="section-header">
        <div class="title-wrapper">
          <h3 class="section-title">数据概览</h3>
          <el-button size="mini" icon="el-icon-refresh" circle @click="fetchOverviewData" :loading="loading.overview"></el-button>
        </div>
      </el-col>

      <!-- 今日收款 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
        <el-card class="overview-card primary">
          <div class="card-content">
            <div class="icon-wrapper">
              <i class="el-icon-money"></i>
            </div>
            <div class="data-wrapper">
              <div class="card-title">今日收款</div>
              <div class="channel-data">
                <div class="channel-item wx">
                  <span class="channel-label">微信</span>
                  <span class="channel-value">{{ getWxAmount(0) }}</span>
                </div>
                <div class="channel-item ali">
                  <span class="channel-label">支付宝</span>
                  <span class="channel-value">{{ getAliAmount(0) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 今日订单 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
        <el-card class="overview-card success">
          <div class="card-content">
            <div class="icon-wrapper">
              <i class="el-icon-shopping-cart-full"></i>
            </div>
            <div class="data-wrapper">
              <div class="card-title">今日订单</div>
              <div class="channel-data">
                <div class="channel-item wx">
                  <span class="channel-label">微信</span>
                  <span class="channel-value">{{ getWxValue(1) }}</span>
                </div>
                <div class="channel-item ali">
                  <span class="channel-label">支付宝</span>
                  <span class="channel-value">{{ getAliValue(1) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 今日成功率 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
        <el-card class="overview-card warning">
          <div class="card-content">
            <div class="icon-wrapper">
              <i class="el-icon-data-line"></i>
            </div>
            <div class="data-wrapper">
              <div class="card-title">今日成功率</div>
              <div class="channel-data">
                <div class="channel-item wx">
                  <span class="channel-label">微信</span>
                  <span class="channel-value">{{ getWxRate(2) }}</span>
                </div>
                <div class="channel-item ali">
                  <span class="channel-label">支付宝</span>
                  <span class="channel-value">{{ getAliRate(2) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 今日放量 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
        <el-card class="overview-card info">
          <div class="card-content">
            <div class="icon-wrapper">
              <i class="el-icon-connection"></i>
            </div>
            <div class="data-wrapper">
              <div class="card-title">今日放量</div>
              <div class="channel-data">
                <div class="channel-item wx">
                  <span class="channel-label">微信</span>
                  <span class="channel-value">{{ getWxAmount(3) }}</span>
                </div>
                <div class="channel-item ali">
                  <span class="channel-label">支付宝</span>
                  <span class="channel-value">{{ getAliAmount(3) }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 月度数据概览 -->
    <el-row :gutter="20" class="data-overview" v-loading="loadingMonthly">
      <!-- 本月收款 -->
      <el-col :xs="24" :sm="12" :md="12" :lg="6" :xl="6">
        <el-card class="overview-card primary">
          <div class="card-content">
            <div class="icon-wrapper">
              <i class="el-icon-date"></i>
            </div>
            <div class="data-wrapper">
              <div class="card-title">本月收款</div>
              <div class="channel-data">
                <div class="channel-item wx">
                  <span class="channel-label">微信</span>
                  <span class="channel-value">{{ getWxMonthlyAmount('current') }}</span>
                </div>
                <div class="channel-item ali">
                  <span class="channel-label">支付宝</span>
                  <span class="channel-value">{{ getAliMonthlyAmount('current') }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

      <div v-if="(!overviewData.wx || overviewData.wx.length === 0) && (!overviewData.ali || overviewData.ali.length === 0) && !loading.overview" class="empty-state">
        <i class="el-icon-document"></i>
        <p>暂无数据</p>
      </div>

    <!-- 美化后的最近一小时成功率和最近5分钟订单笔数 -->
    <el-row :gutter="20" class="custom-overview-row">
      <!-- 最近一小时成功率 -->
      <el-col :xs="24" :sm="12">
        <div class="custom-card gradient-blue">
          <div class="section-header-flex">
            <span class="section-title">最近一小时成功率</span>
            <el-button
              size="mini"
              icon="el-icon-refresh"
              circle
              :loading="refreshing.lastHour"
              @click="refreshLastHour"
              class="refresh-btn"
            ></el-button>
          </div>
          <div class="card-main paytype-double">
            <div class="paytype-block">
              <i class="el-icon-data-analysis main-icon" style="color:#07C160;background:#e8f8f2;"></i>
              <div class="main-value">
                <span>{{ huyaHourSuccessRate }}<span v-if="huyaHourSuccessRate !== '--'">%</span></span>
              </div>
              <div class="sub-info">微信</div>
              <div class="sub-info" v-if="huyaHourTime">{{ huyaHourTime }}</div>
            </div>
            <div class="paytype-block">
              <i class="el-icon-data-analysis main-icon" style="color:#1677FF;background:#eaf3fb;"></i>
              <div class="main-value">
                <span>{{ yahuHourSuccessRate }}<span v-if="yahuHourSuccessRate !== '--'">%</span></span>
              </div>
              <div class="sub-info">支付宝</div>
              <div class="sub-info" v-if="yahuHourTime">{{ yahuHourTime }}</div>
            </div>
          </div>
        </div>
      </el-col>
      <!-- 最近5分钟订单笔数 -->
      <el-col :xs="24" :sm="12">
        <div class="custom-card gradient-green">
          <div class="section-header-flex">
            <span class="section-title">最近5分钟订单笔数</span>
            <el-button
              size="mini"
              icon="el-icon-refresh"
              circle
              :loading="refreshing.last5min"
              @click="refreshLast5Min"
              class="refresh-btn"
            ></el-button>
          </div>
          <div class="card-main paytype-double">
            <div class="paytype-block">
              <i class="el-icon-timer main-icon" style="color:#07C160;background:#e8f8f2;"></i>
              <div class="main-value">
                <span>{{ huya5minOrder }}</span>
              </div>
              <div class="sub-info">微信</div>
              <div class="sub-info" v-if="huya5minTime">{{ huya5minTime }}</div>
            </div>
            <div class="paytype-block">
              <i class="el-icon-timer main-icon" style="color:#1677FF;background:#eaf3fb;"></i>
              <div class="main-value">
                <span>{{ yahu5minOrder }}</span>
              </div>
              <div class="sub-info">支付宝</div>
              <div class="sub-info" v-if="yahu5minTime">{{ yahu5minTime }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 历史数据 -->
    <el-row class="monitor-section">
      <el-col :span="24">
        <el-card class="channel-table-card" v-loading="loading.channel">
          <div slot="header" class="card-header">
            <span class="card-title">历史数据</span>
            <div class="header-right">
              <el-date-picker
                v-model="channelDateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                size="small"
                style="margin-right: 10px;"
                class="date-picker"
                @change="onDateRangeChange"
              ></el-date-picker>
              <el-button size="small" icon="el-icon-refresh" circle @click="fetchChannelData" :loading="loading.channel"></el-button>
            </div>
          </div>
          <div class="table-responsive">
            <el-row :gutter="20">
              <!-- 微信支付数据表格 -->
              <el-col :xs="24" :sm="24" :md="12">
                <div class="table-section">
                  <h4 class="table-title">微信支付</h4>
                  <el-table
                    :data="channelData.wechat"
                    style="width: 100%"
                    size="small"
                    border
                    height="300"
                    v-if="channelData.wechat && channelData.wechat.length > 0"
                  >
                    <el-table-column prop="date" label="日期" min-width="120" sortable>
                      <template slot-scope="scope">
                        {{ scope.row.date }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="volumeAmount" label="放量金额" min-width="140" sortable>
                      <template slot-scope="scope">
                        <span class="amount">¥ {{ scope.row.volumeAmount.toLocaleString() }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="amount" label="收款金额" min-width="140" sortable>
                      <template slot-scope="scope">
                        <span class="amount highlight">¥ {{ scope.row.amount.toLocaleString() }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="orderCount" label="总订单数" min-width="120" sortable>
                      <template slot-scope="scope">
                        {{ scope.row.orderCount.toLocaleString() }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="successOrderCount" label="成功订单数" min-width="120" sortable>
                      <template slot-scope="scope">
                        {{ scope.row.successOrderCount.toLocaleString() }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="successRate" label="成功率" min-width="100" sortable>
                      <template slot-scope="scope">
                        <span :style="{ color: getSuccessRateColor(scope.row.successRate) }">
                          {{ scope.row.successRate }}%
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div v-if="!channelData.wechat || channelData.wechat.length === 0" class="empty-state">
                    <i class="el-icon-document"></i>
                    <p>暂无微信支付数据</p>
                  </div>
                </div>
              </el-col>

              <!-- 支付宝数据表格 -->
              <el-col :xs="24" :sm="24" :md="12">
                <div class="table-section">
                  <h4 class="table-title">支付宝</h4>
                  <el-table
                    :data="channelData.alipay"
                    style="width: 100%"
                    size="small"
                    border
                    height="300"
                    v-if="channelData.alipay && channelData.alipay.length > 0"
                  >
                    <el-table-column prop="date" label="日期" min-width="120" sortable>
                      <template slot-scope="scope">
                        {{ scope.row.date }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="volumeAmount" label="放量金额" min-width="140" sortable>
                      <template slot-scope="scope">
                        <span class="amount">¥ {{ scope.row.volumeAmount.toLocaleString() }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="amount" label="收款金额" min-width="140" sortable>
                      <template slot-scope="scope">
                        <span class="amount highlight">¥ {{ scope.row.amount.toLocaleString() }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="orderCount" label="总订单数" min-width="120" sortable>
                      <template slot-scope="scope">
                        {{ scope.row.orderCount.toLocaleString() }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="successOrderCount" label="成功订单数" min-width="120" sortable>
                      <template slot-scope="scope">
                        {{ scope.row.successOrderCount.toLocaleString() }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="successRate" label="成功率" min-width="100" sortable>
                      <template slot-scope="scope">
                        <span :style="{ color: getSuccessRateColor(scope.row.successRate) }">
                          {{ scope.row.successRate }}%
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                  <div v-if="!channelData.alipay || channelData.alipay.length === 0" class="empty-state">
                    <i class="el-icon-document"></i>
                    <p>暂无支付宝数据</p>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getDashboardOverview, getDashboardOverviewAli, getChannelData, getRecentHourSuccessRate, getRecentFiveMinuteOrderCount, getMonthlyRevenueData, getMonthlyRevenueDataAli } from '@/api/dashboard'

export default {
  name: 'Dashboard',
  data() {
    return {
      timeRange: 'today',
      overviewData: {
        wx: [],
        ali: []
      },
      pickerOptions: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      channelDateRange: '',
      channelData: {
        wechat: [],
        alipay: []
      },
      loading: {
        overview: false,
        channel: false
      },
      lastHourSuccessRateData: { huya: null, yahu: null }, // 最近一小时成功率数据
      last5MinOrderCountData: { huya: null, yahu: null }, // 最近5分钟订单笔数数据
      refreshing: {
        lastHour: false,
        last5min: false
      },
      monthlyData: {
        wx: { currentMonth: 0 },
        ali: { currentMonth: 0 }
      },
      loadingMonthly: false
    }
  },
  created() {
    this.fetchOverviewData()
    // Set default date range to last 7 days
    const end = new Date()
    const start = new Date()
    start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
    this.channelDateRange = [start, end]
    this.fetchChannelData()
    this.fetchLastHourSuccessRate()
    this.fetchLast5MinOrderCount()
    this.fetchMonthlyData()
  },
  watch: {
    channelDateRange() {
      if (this.channelDateRange && this.channelDateRange.length === 2) {
        this.fetchChannelData()
      }
    }
  },
  methods: {
    // 获取概览数据
    fetchOverviewData() {
      this.loading.overview = true
      Promise.all([
        getDashboardOverview(),
        getDashboardOverviewAli()
      ])
        .then(([wxResponse, aliResponse]) => {
          if (wxResponse.data && aliResponse.data) {
            // 微信数据
            const wxData = wxResponse.data
            const wxOverviewData = [
              {
                type: 'primary',
                icon: 'el-icon-money',
                value: '¥' + this.formatNumber(wxData.todayAmount),
                label: '今日收款'
              },
              {
                type: 'success',
                icon: 'el-icon-shopping-cart-full',
                value: this.formatNumber(wxData.todayOrderCount),
                label: '今日订单'
              },
              {
                type: 'warning',
                icon: 'el-icon-data-line',
                value: wxData.todaySuccessRate + '%',
                label: '今日成功率'
              },
              {
                type: 'info',
                icon: 'el-icon-connection',
                value: '¥' + this.formatNumber(wxData.todayOrderTotalAmount),
                label: '今日放量'
              }
            ]

            // 支付宝数据
            const aliData = aliResponse.data
            const aliOverviewData = [
              {
                type: 'primary',
                icon: 'el-icon-money',
                value: '¥' + this.formatNumber(aliData.todayAmount),
                label: '今日收款'
              },
              {
                type: 'success',
                icon: 'el-icon-shopping-cart-full',
                value: this.formatNumber(aliData.todayOrderCount),
                label: '今日订单'
              },
              {
                type: 'warning',
                icon: 'el-icon-data-line',
                value: aliData.todaySuccessRate + '%',
                label: '今日成功率'
              },
              {
                type: 'info',
                icon: 'el-icon-connection',
                value: '¥' + this.formatNumber(aliData.todayOrderTotalAmount),
                label: '今日放量'
              }
            ]

            this.overviewData = {
              wx: wxOverviewData,
              ali: aliOverviewData
            }
          }
        })
        .catch(() => {
          this.$message.error('获取概览数据失败')
        })
        .finally(() => {
          this.loading.overview = false
        })
    },
    // 获取通道数据
    fetchChannelData() {
      this.loading.channel = true
      const params = {}

      if (this.channelDateRange && this.channelDateRange.length === 2) {
        params.startDate = this.formatDate(this.channelDateRange[0])
        params.endDate = this.formatDate(this.channelDateRange[1])
      }

      getChannelData(params)
        .then(response => {
          if (response.data) {
            this.channelData = response.data
          }
        })
        .catch(() => {
          this.$message.error('获取通道数据失败')
        })
        .finally(() => {
          this.loading.channel = false
        })
    },
    // 格式化日期为 YYYY-MM-DD
    formatDate(date) {
      const d = new Date(date)
      const year = d.getFullYear()
      const month = String(d.getMonth() + 1).padStart(2, '0')
      const day = String(d.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    // 格式化数字，添加千位分隔符
    formatNumber(num) {
      return Number(num).toLocaleString()
    },
    getSuccessRateColor(percentage) {
      if (percentage < 90) return '#F56C6C'
      if (percentage < 95) return '#E6A23C'
      return '#67C23A'
    },
    onDateRangeChange() {
      if (this.channelDateRange && this.channelDateRange.length === 2) {
        this.fetchChannelData()
      }
    },
    // 获取微信金额数据
    getWxAmount(index) {
      if (!this.overviewData.wx || !this.overviewData.wx[index]) {
        return '¥0'
      }
      const value = this.overviewData.wx[index].value
      // 直接返回格式化后的值，不需要重复处理
      return value
    },

    // 获取支付宝金额数据
    getAliAmount(index) {
      if (!this.overviewData.ali || !this.overviewData.ali[index]) {
        return '¥0'
      }
      const value = this.overviewData.ali[index].value
      // 直接返回格式化后的值，不需要重复处理
      return value
    },

    // 获取微信普通数值
    getWxValue(index) {
      if (!this.overviewData.wx || !this.overviewData.wx[index]) {
        return '0'
      }
      const value = this.overviewData.wx[index].value
      // 直接返回格式化后的值，不需要重复处理
      return value
    },

    // 获取支付宝普通数值
    getAliValue(index) {
      if (!this.overviewData.ali || !this.overviewData.ali[index]) {
        return '0'
      }
      const value = this.overviewData.ali[index].value
      // 直接返回格式化后的值，不需要重复处理
      return value
    },

    // 获取微信成功率
    getWxRate(index) {
      const value = this.overviewData.wx && this.overviewData.wx[index] ? this.overviewData.wx[index].value : '0'
      return value.toString().replace('%', '') + '%'
    },

    // 获取支付宝成功率
    getAliRate(index) {
      const value = this.overviewData.ali && this.overviewData.ali[index] ? this.overviewData.ali[index].value : '0'
      return value.toString().replace('%', '') + '%'
    },
    fetchLastHourSuccessRate() {
      getRecentHourSuccessRate().then(res => {
        if (res.data && res.data.huya && res.data.yahu) {
          this.lastHourSuccessRateData = res.data
        } else {
          this.lastHourSuccessRateData = { huya: null, yahu: null }
        }
      }).catch(() => {
        this.lastHourSuccessRateData = { huya: null, yahu: null }
      })
    },
    fetchLast5MinOrderCount() {
      getRecentFiveMinuteOrderCount().then(res => {
        if (res.data && res.data.huya && res.data.yahu) {
          this.last5MinOrderCountData = res.data
        } else {
          this.last5MinOrderCountData = { huya: null, yahu: null }
        }
      }).catch(() => {
        this.last5MinOrderCountData = { huya: null, yahu: null }
      })
    },
    refreshLastHour() {
      this.refreshing.lastHour = true
      this.fetchLastHourSuccessRate()
      setTimeout(() => {
        this.refreshing.lastHour = false
      }, 800)
    },
    refreshLast5Min() {
      this.refreshing.last5min = true
      this.fetchLast5MinOrderCount()
      setTimeout(() => {
        this.refreshing.last5min = false
      }, 800)
    },
    // 获取月度收款数据
    fetchMonthlyData() {
      this.loadingMonthly = true
      Promise.all([
        getMonthlyRevenueData(),
        getMonthlyRevenueDataAli()
      ])
        .then(([wxResponse, aliResponse]) => {
          if (wxResponse.data && aliResponse.data) {
            this.monthlyData = {
              wx: wxResponse.data,
              ali: aliResponse.data
            }
          }
        })
        .catch(() => {
          this.$message.error('获取月度数据失败')
        })
        .finally(() => {
          this.loadingMonthly = false
        })
    },
    // 获取微信月度金额
    getWxMonthlyAmount(type) {
      if (!this.monthlyData.wx) {
        return '¥0'
      }
      return '¥' + this.formatNumber(this.monthlyData.wx.currentMonth || 0)
    },
    // 获取支付宝月度金额
    getAliMonthlyAmount(type) {
      if (!this.monthlyData.ali) {
        return '¥0'
      }
      return '¥' + this.formatNumber(this.monthlyData.ali.currentMonth || 0)
    }
  },
  computed: {
    huyaHourSuccessRate() {
      return this.lastHourSuccessRateData && this.lastHourSuccessRateData.huya && this.lastHourSuccessRateData.huya.successRate !== undefined && this.lastHourSuccessRateData.huya.successRate !== null
        ? this.lastHourSuccessRateData.huya.successRate
        : '--'
    },
    yahuHourSuccessRate() {
      return this.lastHourSuccessRateData && this.lastHourSuccessRateData.yahu && this.lastHourSuccessRateData.yahu.successRate !== undefined && this.lastHourSuccessRateData.yahu.successRate !== null
        ? this.lastHourSuccessRateData.yahu.successRate
        : '--'
    },
    huyaHourTime() {
      const d = this.lastHourSuccessRateData && this.lastHourSuccessRateData.huya
        ? this.lastHourSuccessRateData.huya
        : null
      return d && d.startTime && d.endTime ? `${d.startTime} ~ ${d.endTime}` : ''
    },
    yahuHourTime() {
      const d = this.lastHourSuccessRateData && this.lastHourSuccessRateData.yahu
        ? this.lastHourSuccessRateData.yahu
        : null
      return d && d.startTime && d.endTime ? `${d.startTime} ~ ${d.endTime}` : ''
    },
    huya5minOrder() {
      return this.last5MinOrderCountData && this.last5MinOrderCountData.huya && this.last5MinOrderCountData.huya.orderCount !== undefined && this.last5MinOrderCountData.huya.orderCount !== null
        ? this.last5MinOrderCountData.huya.orderCount
        : '--'
    },
    yahu5minOrder() {
      return this.last5MinOrderCountData && this.last5MinOrderCountData.yahu && this.last5MinOrderCountData.yahu.orderCount !== undefined && this.last5MinOrderCountData.yahu.orderCount !== null
        ? this.last5MinOrderCountData.yahu.orderCount
        : '--'
    },
    huya5minTime() {
      const d = this.last5MinOrderCountData && this.last5MinOrderCountData.huya
        ? this.last5MinOrderCountData.huya
        : null
      return d && d.startTime && d.endTime ? `${d.startTime} ~ ${d.endTime}` : ''
    },
    yahu5minTime() {
      const d = this.last5MinOrderCountData && this.last5MinOrderCountData.yahu
        ? this.last5MinOrderCountData.yahu
        : null
      return d && d.startTime && d.endTime ? `${d.startTime} ~ ${d.endTime}` : ''
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background: #f0f2f5;
  min-height: calc(100vh - 50px);

  @media screen and (max-width: 768px) {
    padding: 10px;
  }

  .section-header {
    margin-bottom: 15px;
    .title-wrapper {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .section-title {
        margin: 0;
        font-size: 18px;
        font-weight: 500;
        color: #303133;
      }
    }
  }

  .data-overview {
    margin-bottom: 20px;

    .overview-card {
      height: auto;
      min-height: 120px;
      transition: all 0.3s;
      margin-bottom: 20px;

      @media screen and (max-width: 768px) {
        min-height: 100px;
      }

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      }

      .card-content {
        display: flex;
        align-items: flex-start;
        height: 100%;
        position: relative;
        padding: 15px;

        @media screen and (max-width: 576px) {
          padding: 12px;
        }

        .icon-wrapper {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          flex-shrink: 0;

          @media screen and (max-width: 576px) {
            width: 40px;
            height: 40px;
            margin-right: 12px;
          }

          i {
            font-size: 24px;
            color: #fff;

            @media screen and (max-width: 576px) {
              font-size: 20px;
            }
          }
        }

        .data-wrapper {
          flex: 1;
          min-width: 0;

          .card-title {
            font-size: 16px;
            font-weight: 500;
            color: #303133;
            margin-bottom: 12px;
          }

          .channel-data {
            .channel-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .channel-label {
                font-size: 14px;
                color: #909399;
                display: flex;
                align-items: center;

                &::before {
                  content: '';
                  display: inline-block;
                  width: 6px;
                  height: 6px;
                  border-radius: 50%;
                  margin-right: 6px;
                }
              }

              .channel-value {
                font-size: 16px;
                font-weight: 500;
                font-family: Monaco, monospace;
              }

              &.wx {
                .channel-label::before {
                  background-color: #07C160;
                }
                .channel-value {
                  color: #07C160;
                }
              }

              &.ali {
                .channel-label::before {
                  background-color: #1677FF;
                }
                .channel-value {
                  color: #1677FF;
                }
              }
            }
          }
        }
      }

      &.primary .icon-wrapper {
        background: rgba(64, 158, 255, 0.1);
        i { color: #409EFF; }
      }
      &.success .icon-wrapper {
        background: rgba(103, 194, 58, 0.1);
        i { color: #67C23A; }
      }
      &.warning .icon-wrapper {
        background: rgba(230, 162, 60, 0.1);
        i { color: #E6A23C; }
      }
      &.info .icon-wrapper {
        background: rgba(144, 147, 153, 0.1);
        i { color: #909399; }
      }
    }
  }

  .monitor-section {
    margin-bottom: 20px;

    .data-table-card {
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
      }

      .table-footer {
        margin-top: 20px;
        display: flex;
        justify-content: flex-end;
      }

      .amount {
        font-family: Monaco, monospace;
        color: #606266;

        &.highlight {
          color: #409EFF;
          font-weight: bold;
        }
      }

      .up {
        color: #67C23A;
      }

      .down {
        color: #F56C6C;
      }
    }
  }

  .channel-table-card {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      flex-wrap: wrap;

      @media screen and (max-width: 768px) {
        flex-direction: column;
        align-items: flex-start;
      }

      .card-title {
        font-weight: bold;
        margin-bottom: 10px;
      }
    }

    .table-footer {
      margin-top: 20px;
      display: flex;
      justify-content: flex-end;
    }

    .amount {
      font-family: Monaco, monospace;
      color: #606266;

      &.highlight {
        color: #409EFF;
        font-weight: bold;
      }
    }

    ::v-deep .el-card__body {
      padding: 15px;
    }
  }
}

.header-right {
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  @media screen and (max-width: 768px) {
    margin-top: 10px;
    width: 100%;
  }

  @media screen and (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
  }

  .date-picker {
    @media screen and (max-width: 576px) {
      margin-bottom: 10px;
      margin-right: 0;
      width: 100%;
    }
  }
}

.table-responsive {
  width: 100%;
  overflow: hidden;

  .el-row {
    margin: 0 !important;
  }

  .el-col {
    padding: 0 10px !important;
  }
}

.group-title {
  margin: 20px 0 15px;
  padding-left: 10px;
  border-left: 4px solid #409EFF;

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }
}

.table-section {
  margin-bottom: 0;
  height: 100%;

  .table-title {
    margin: 0 0 15px;
    padding-left: 10px;
    border-left: 4px solid #409EFF;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .el-table {
    margin-bottom: 0;

    // 自定义滚动条样式
    ::v-deep .el-table__body-wrapper {
      &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c0c4cc;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background: #f5f7fa;
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .table-section {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 40px 0;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 16px;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}
.card-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}
.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  background: rgba(64, 158, 255, 0.1);
}
.icon-wrapper i {
  font-size: 24px;
  color: #409EFF;
}

.custom-overview-row {
  margin-bottom: 24px;
}

.custom-card {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.card-main.paytype-double {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 12px;
  width: 100%;
  flex: 1 1 auto;
  min-height: 180px;
}
.paytype-block {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  min-height: 140px;
  height: 100%;
}
.paytype-block .main-icon {
  margin-bottom: 8px;
}
.paytype-block .main-value {
  font-size: 38px;
  font-weight: bold;
  color: #222;
  margin: 0 0 14px 0;
  text-align: center;
  line-height: 1.1;
}
.paytype-block .sub-info {
  font-size: 13px;
  color: #909399;
  margin-bottom: 2px;
  text-align: center;
  line-height: 1.4;
  margin-top: 0;
}
.paytype-block .sub-info:last-child {
  margin-top: 4px;
  color: #b0b0b0;
  font-size: 12px;
}

.gradient-blue,
.gradient-green {
  background: #fff !important;
}

@media (max-width: 768px) {
  .custom-card {
    padding: 14px 8px 12px 8px;
    min-height: 90px;
    .card-main .main-value {
      font-size: 20px;
      min-width: 40px;
    }
    .card-main .main-icon {
      font-size: 22px;
      margin-right: 8px;
      padding: 4px;
    }
  }
}

.paytype-double {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  gap: 12px;
  width: 100%;
}
.paytype-block {
  flex: 1;
  background: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  min-height: 140px;
}
.paytype-block .main-icon {
  margin-bottom: 8px;
}
.paytype-block .main-value {
  font-size: 38px;
  font-weight: bold;
  color: #222;
  margin: 0 0 14px 0;
  text-align: center;
  line-height: 1.1;
}
.paytype-block .sub-info {
  font-size: 13px;
  color: #909399;
  margin-bottom: 2px;
  text-align: center;
  line-height: 1.4;
  margin-top: 0;
}
.paytype-block .sub-info:last-child {
  margin-top: 4px;
  color: #b0b0b0;
  font-size: 12px;
}
@media (max-width: 768px) {
  .paytype-double {
    flex-direction: column;
    gap: 6px;
  }
  .paytype-block {
    align-items: center;
    min-height: 100px;
  }
}

.section-header-flex {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 18px;
  padding: 18px 24px 0 24px;
  .section-title {
    font-size: 17px;
    font-weight: 600;
    color: #222;
    margin-right: 10px;
  }
  .refresh-btn {
    margin-left: 0;
    margin-right: 4px;
  }
}
.paytype-block .main-icon {
  font-size: 38px;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}
</style>
