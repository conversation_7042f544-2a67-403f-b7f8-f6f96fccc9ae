<template>
  <div class="login-container">
    <!-- 添加背景动画元素 -->
    <div class="bg-animation-area">
      <ul class="circles">
        <li v-for="n in 10" :key="n"></li>
      </ul>
    </div>

    <div class="login-content">
      <div class="illustration-container">
        <div class="illustration">
          <!-- SVG illustration embedded directly for tech-inspired design -->
          <svg viewBox="0 0 500 500" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" style="stop-color:#4facfe;stop-opacity:1" />
                <stop offset="100%" style="stop-color:#00f2fe;stop-opacity:1" />
              </linearGradient>
              <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
                <feGaussianBlur stdDeviation="10" result="blur" />
                <feComposite in="SourceGraphic" in2="blur" operator="over" />
              </filter>
            </defs>
            <!-- Tech-inspired abstract shapes -->
            <circle cx="250" cy="250" r="120" fill="url(#grad1)" opacity="0.8" filter="url(#glow)" />
            <path d="M100,100 L400,100 L400,400 L100,400 Z" stroke="white" stroke-width="2" fill="none" opacity="0.5" />
            <path d="M150,150 L350,150 L350,350 L150,350 Z" stroke="white" stroke-width="2" fill="none" opacity="0.7" />
            <circle cx="250" cy="250" r="50" fill="none" stroke="white" stroke-width="2" />
            <circle cx="250" cy="250" r="80" fill="none" stroke="white" stroke-width="1" opacity="0.5" />
            <circle cx="250" cy="250" r="150" fill="none" stroke="white" stroke-width="1" opacity="0.3" />
            <path d="M100,250 L400,250" stroke="white" stroke-width="1" opacity="0.5" />
            <path d="M250,100 L250,400" stroke="white" stroke-width="1" opacity="0.5" />

            <!-- 添加一些动态元素 -->
            <circle cx="250" cy="250" r="180" fill="none" stroke="white" stroke-width="1" opacity="0.2" class="pulse-circle" />
            <circle cx="250" cy="250" r="200" fill="none" stroke="white" stroke-width="1" opacity="0.1" class="pulse-circle-delay" />

            <!-- 添加一些小点装饰 -->
            <g class="floating-dots">
              <circle cx="150" cy="150" r="3" fill="white" opacity="0.8" class="float-dot" />
              <circle cx="350" cy="150" r="2" fill="white" opacity="0.6" class="float-dot" />
              <circle cx="150" cy="350" r="2" fill="white" opacity="0.7" class="float-dot" />
              <circle cx="350" cy="350" r="3" fill="white" opacity="0.8" class="float-dot" />
              <circle cx="250" cy="150" r="2" fill="white" opacity="0.9" class="float-dot" />
              <circle cx="150" cy="250" r="2" fill="white" opacity="0.7" class="float-dot" />
              <circle cx="350" cy="250" r="2" fill="white" opacity="0.8" class="float-dot" />
              <circle cx="250" cy="350" r="2" fill="white" opacity="0.6" class="float-dot" />
            </g>
          </svg>
        </div>
        <h2 class="welcome-text">欢迎回来</h2>
        <p class="system-name">管理系统</p>
      </div>

      <div class="form-container">
        <div class="glass-effect"></div>
        <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" auto-complete="on" label-position="left">
          <div class="title-container">
            <h3 class="title">用户登录</h3>
            <p class="subtitle">请输入您的账号和密码</p>
          </div>

          <el-form-item prop="username">
            <span class="svg-container">
              <svg-icon icon-class="user" />
            </span>
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="用户名"
              name="username"
              type="text"
              tabindex="1"
              auto-complete="on"
            />
          </el-form-item>

          <el-form-item prop="password">
            <span class="svg-container">
              <svg-icon icon-class="password" />
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="密码"
              name="password"
              tabindex="2"
              auto-complete="on"
              @keyup.enter.native="handleLogin"
            />
            <span class="show-pwd" @click="showPwd">
              <svg-icon :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'" />
            </span>
          </el-form-item>

          <el-button :loading="loading" type="primary" class="login-button" @click.native.prevent="handleLogin">
            <span v-if="!loading">登录</span>
            <span v-else>登录中...</span>
          </el-button>
        </el-form>

        <!-- 添加版权信息 -->
        <div class="copyright">
          © {{ new Date().getFullYear() }} 管理系统 - 版权所有
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'

export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('请输入正确的用户名'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6) {
        callback(new Error('密码不能少于6位'))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [{ required: true, trigger: 'blur', validator: validateUsername }],
        password: [{ required: true, trigger: 'blur', validator: validatePassword }]
      },
      loading: false,
      passwordType: 'password',
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.password.focus()
      })
    },
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          setTimeout(() => {
            this.$store.dispatch('user/login', this.loginForm).then(() => {
              this.$router.push({ path: this.redirect || '/' })
              this.loading = false
            }).catch(() => {
              this.loading = false
            })
          }, 600) // 添加一点延迟以展示加载效果
        } else {
          console.log('登录失败!')
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss">
/* 修复input 背景不协调 和光标变色 */
/* Detail see https://github.com/PanJiaChen/vue-element-admin/pull/927 */

$primary-color: #4facfe;
$secondary-color: #00f2fe;
$bg-color: #f0f5ff;
$text-color: #333;
$input-bg: rgba(255, 255, 255, 0.9);
$cursor: $primary-color;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .login-container .el-input input {
    color: $text-color;
  }
}

/* reset element-ui css */
.login-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: $input-bg;
      border: 0px;
      -webkit-appearance: none;
      border-radius: 0px;
      padding: 12px 5px 12px 15px;
      color: $text-color;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0px 1000px $input-bg inset !important;
        -webkit-text-fill-color: $text-color !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(79, 172, 254, 0.2);
    background: $input-bg;
    border-radius: 8px;
    margin-bottom: 20px;
    transition: all 0.3s;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);

    &:hover, &:focus-within {
      box-shadow: 0 0 15px rgba(79, 172, 254, 0.4);
      border: 1px solid rgba(79, 172, 254, 0.5);
    }
  }

  .el-button--primary {
    background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    border-color: $primary-color;
    border-radius: 8px;
    height: 50px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0) 100%
      );
      transition: all 0.8s;
    }

    &:hover, &:focus {
      background: linear-gradient(135deg, lighten($primary-color, 5%) 0%, lighten($secondary-color, 5%) 100%);
      box-shadow: 0 7px 20px rgba(79, 172, 254, 0.4);
      transform: translateY(-2px);

      &:before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
    }
  }
}
</style>

<style lang="scss" scoped>
$primary-color: #4facfe;
$secondary-color: #00f2fe;
$bg-color: #f0f5ff;
$text-color: #333;
$light-text: #666;

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.2;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

@keyframes animate {
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 1;
    border-radius: 0;
  }
  100% {
    transform: translateY(-1000px) rotate(720deg);
    opacity: 0;
    border-radius: 50%;
  }
}

.login-container {
  min-height: 100%;
  width: 100%;
  background: linear-gradient(to bottom, #e6f0ff, #f0f5ff);
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;

  .bg-animation-area {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 0;
  }

  .circles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    margin: 0;
    padding: 0;

    li {
      position: absolute;
      display: block;
      list-style: none;
      width: 20px;
      height: 20px;
      background: rgba(79, 172, 254, 0.2);
      animation: animate 25s linear infinite;
      bottom: -150px;

      &:nth-child(1) {
        left: 25%;
        width: 80px;
        height: 80px;
        animation-delay: 0s;
      }

      &:nth-child(2) {
        left: 10%;
        width: 20px;
        height: 20px;
        animation-delay: 2s;
        animation-duration: 12s;
      }

      &:nth-child(3) {
        left: 70%;
        width: 20px;
        height: 20px;
        animation-delay: 4s;
      }

      &:nth-child(4) {
        left: 40%;
        width: 60px;
        height: 60px;
        animation-delay: 0s;
        animation-duration: 18s;
      }

      &:nth-child(5) {
        left: 65%;
        width: 20px;
        height: 20px;
        animation-delay: 0s;
      }

      &:nth-child(6) {
        left: 75%;
        width: 110px;
        height: 110px;
        animation-delay: 3s;
      }

      &:nth-child(7) {
        left: 35%;
        width: 150px;
        height: 150px;
        animation-delay: 7s;
      }

      &:nth-child(8) {
        left: 50%;
        width: 25px;
        height: 25px;
        animation-delay: 15s;
        animation-duration: 45s;
      }

      &:nth-child(9) {
        left: 20%;
        width: 15px;
        height: 15px;
        animation-delay: 2s;
        animation-duration: 35s;
      }

      &:nth-child(10) {
        left: 85%;
        width: 150px;
        height: 150px;
        animation-delay: 0s;
        animation-duration: 11s;
      }
    }
  }

  .login-content {
    display: flex;
    width: 900px;
    height: 600px;
    background: rgba(255, 255, 255, 0.25);
    border-radius: 16px;
    box-shadow: 0 25px 45px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    overflow: hidden;
    z-index: 1;
  }

  .illustration-container {
    flex: 1;
    background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    padding: 40px;
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 60%);
      transform: rotate(30deg);
    }

    .illustration {
      width: 100%;
      height: 300px;
      margin-bottom: 30px;
      position: relative;

      .pulse-circle {
        animation: pulse 4s infinite;
      }

      .pulse-circle-delay {
        animation: pulse 4s infinite;
        animation-delay: 2s;
      }

      .float-dot {
        animation: float 6s ease-in-out infinite;

        &:nth-child(2n) {
          animation-delay: 1s;
        }

        &:nth-child(3n) {
          animation-delay: 2s;
        }

        &:nth-child(4n) {
          animation-delay: 3s;
        }
      }
    }

    .welcome-text {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 10px;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      background: linear-gradient(to right, #ffffff, #e6f0ff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      position: relative;

      &:after {
        content: '';
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 2px;
        background: white;
      }
    }

    .system-name {
      font-size: 18px;
      opacity: 0.9;
      margin-bottom: 30px;
      letter-spacing: 1px;
    }
  }

  .form-container {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    background: rgba(255, 255, 255, 0.7);

    .glass-effect {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      z-index: -1;
    }
  }

  .login-form {
    width: 100%;
    max-width: 100%;
    padding: 0;
    margin: 0 auto;
    position: relative;
    z-index: 2;
  }

  .login-button {
    width: 100%;
    margin-bottom: 20px;
  }

  .svg-container {
    padding: 6px 5px 6px 15px;
    color: $primary-color;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .title-container {
    position: relative;
    margin-bottom: 40px;

    .title {
      font-size: 28px;
      color: $text-color;
      margin: 0px auto 10px auto;
      text-align: center;
      font-weight: 600;
      background: linear-gradient(to right, $primary-color, $secondary-color);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    .subtitle {
      font-size: 14px;
      color: $light-text;
      text-align: center;
      margin: 0;
    }
  }

  .show-pwd {
    position: absolute;
    right: 10px;
    top: 7px;
    font-size: 16px;
    color: $primary-color;
    cursor: pointer;
    user-select: none;
  }

  .copyright {
    position: absolute;
    bottom: 15px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 12px;
    color: $light-text;
  }
}

@media (max-width: 768px) {
  .login-container {
    .login-content {
      flex-direction: column;
      width: 95%;
      height: auto;
    }

    .illustration-container {
      padding: 30px;

      .illustration {
        height: 200px;
        margin-bottom: 20px;
      }
    }

    .form-container {
      padding: 30px;
    }
  }
}
</style>
