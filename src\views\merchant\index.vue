<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="filter-container">
        <div class="filter-row">
          <el-input
            v-model="listQuery.name"
            placeholder="商户名称"
            class="filter-item search-input"
            @keyup.enter.native="handleFilter"
          />
          <el-select
            v-model="listQuery.status"
            placeholder="状态"
            clearable
            class="filter-item status-select"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            搜索
          </el-button>
          <el-button
            class="filter-item"
            type="success"
            icon="el-icon-plus"
            @click="handleAdd"
          >
            新增
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-scroll-x">
        <el-table
          v-loading="loading"
          :data="displayData"
          border
          stripe
          highlight-current-row
          class="merchant-table"
          :style="{ minWidth: '900px' }"
          :span-method="spanMethod"
        >
          <el-table-column label="分组" width="120" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isGroupHeader" class="group-name" :class="{ 'ungrouped': scope.row.groupName === '未分组' }">
                {{ scope.row.groupName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="商户ID" width="80" align="center" />
          <el-table-column label="商户信息" min-width="200" align="center">
            <template slot-scope="scope">
              <div class="merchant-info-cell">
                <div class="merchant-name">{{ scope.row.name }}</div>
                <div class="merchant-key-row">
                  <el-tooltip effect="dark" :content="scope.row.key" placement="top">
                    <span class="secret-key">{{ scope.row.key | formatKey }}</span>
                  </el-tooltip>
                  <el-button
                    type="text"
                    icon="el-icon-copy-document"
                    class="copy-btn"
                    @click="copyKey(scope.row.key)"
                  />
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="账户信息" min-width="140" align="center">
            <template slot-scope="scope">
              <div class="account-info-cell">
                <div class="prepaid-row">
                  <span class="prepaid-label">预付：</span>
                  <span class="prepaid-value">{{ scope.row.current_prepaid | formatRate }}</span>
                  <el-button
                    type="text"
                    icon="el-icon-edit"
                    class="edit-prepaid-btn"
                    @click="handleEditPrepaid(scope.row)"
                  />
                </div>
                <div class="balance-row">
                  <span class="balance-label">余额：</span>
                  <span
                    class="balance-value"
                    :class="{ 'negative-balance': scope.row.prepaid_balance < 0 }"
                  >
                    {{ scope.row.prepaid_balance | formatMoney }}
                  </span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="分组合计" min-width="240" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.isGroupHeader" class="group-summary-card">
                <div class="summary-row">
                  <span class="summary-label">预付</span>
                  <span class="summary-value prepaid">{{ scope.row.groupTotalPrepaid | formatRate }}</span>
                </div>
                <div class="summary-row">
                  <span class="summary-label">余额</span>
                  <span
                    class="summary-value balance"
                    :class="{ 'negative-balance': scope.row.groupTotalBalance < 0 }"
                  >
                    {{ scope.row.groupTotalBalance | formatMoney }}
                  </span>
                </div>
              </div>
              <div v-else class="group-summary-empty">
                <!-- 非分组头部行显示为空 -->
              </div>
            </template>
          </el-table-column>
          <el-table-column label="今日收款" min-width="200" align="center">
            <template slot-scope="scope">
              <div class="amount-cell">
                <div class="amount-item">
                  <span class="amount-label">微信：</span>
                  <span class="amount-value">{{ scope.row.today_wechat_amount | formatMoney }}</span>
                </div>
                <div class="amount-item">
                  <span class="amount-label">支付宝：</span>
                  <span class="amount-value">{{ scope.row.today_alipay_amount | formatMoney }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="昨日收款" min-width="200" align="center">
            <template slot-scope="scope">
              <div class="amount-cell">
                <div class="amount-item">
                  <span class="amount-label">微信：</span>
                  <span class="amount-value">{{ scope.row.yesterday_wechat_amount | formatMoney }}</span>
                </div>
                <div class="amount-item">
                  <span class="amount-label">支付宝：</span>
                  <span class="amount-value">{{ scope.row.yesterday_alipay_amount | formatMoney }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="前日收款" min-width="200" align="center">
            <template slot-scope="scope">
              <div class="amount-cell">
                <div class="amount-item">
                  <span class="amount-label">微信：</span>
                  <span class="amount-value">{{ scope.row.before_yesterday_wechat_amount | formatMoney }}</span>
                </div>
                <div class="amount-item">
                  <span class="amount-label">支付宝：</span>
                  <span class="amount-value">{{ scope.row.before_yesterday_alipay_amount | formatMoney }}</span>
                </div>
              </div>
            </template>
          </el-table-column> -->
          <el-table-column label="限额" min-width="120" align="center">
            <template slot-scope="scope">
              <span class="limit-range">
                {{ formatLimitRange(scope.row.min_amount, scope.row.max_amount) }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" min-width="100" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                :active-value="1"
                :inactive-value="0"
                @change="handleStatusChange(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150" align="center" show-overflow-tooltip />
          <el-table-column prop="telegram_group_id" label="TG群ID" min-width="120" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.telegram_group_id" class="tg-group-id">
                {{ scope.row.telegram_group_id }}
              </span>
              <span v-else class="no-tg-group">未设置</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="216" align="center">
            <template slot-scope="scope">
              <div class="operation-btns">
                <el-button
                  size="mini"
                  type="warning"
                  icon="el-icon-setting"
                  class="operation-btn"
                  @click="handleSetRate(scope.row)"
                >
                  费率
                </el-button>
                <el-button
                  size="mini"
                  type="primary"
                  icon="el-icon-edit"
                  class="operation-btn"
                  @click="handleEdit(scope.row)"
                >
                  编辑
                </el-button>
                <el-button
                  size="mini"
                  type="info"
                  icon="el-icon-info"
                  class="operation-btn"
                  @click="handleDetail(scope.row)"
                >
                  详情
                </el-button>
                <el-button
                  size="mini"
                  type="danger"
                  icon="el-icon-delete"
                  class="operation-btn"
                  @click="handleDelete(scope.row)"
                >
                  删除
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 商户编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="90%" class="mobile-dialog">
      <el-form
        ref="dataForm"
        :model="temp"
        label-position="right"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item label="商户名称" prop="name">
          <el-input v-model="temp.name" placeholder="请输入商户名称" />
        </el-form-item>
        <el-form-item label="密钥" prop="key">
          <el-input v-model="temp.key" placeholder="请输入密钥">
            <el-button slot="append" @click="generateKey">生成</el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio :label="1">开启</el-radio>
            <el-radio :label="0">关闭</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="temp.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
        <el-form-item label="TG群ID" prop="telegram_group_id">
          <el-input
            v-model="temp.telegram_group_id"
            placeholder="请输入TG群ID"
            clearable
          />
        </el-form-item>
        <el-form-item label="最小金额" prop="min_amount">
          <el-input
            v-model="temp.min_amount"
            placeholder="请输入最小金额"
            type="number"
            min="0"
            step="0.01"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="最大金额" prop="max_amount">
          <el-input
            v-model="temp.max_amount"
            placeholder="请输入最大金额"
            type="number"
            min="0"
            step="0.01"
          >
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveData">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 设置费率对话框 -->
    <el-dialog :title="'设置费率'" :visible.sync="setRateDialogVisible" width="90%" class="mobile-dialog">
      <el-form :model="setRateForm" :rules="setRateRules" ref="setRateForm" label-width="90px">
        <el-form-item label="商户ID">
          <el-input v-model="setRateForm.id" readonly />
        </el-form-item>
        <el-form-item label="商户名称">
          <el-input v-model="setRateForm.name" readonly />
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            v-model="setRateForm.time"
            type="datetime"
            placeholder="选择时间"
            style="width: 100%;"
            value-format="timestamp"
          />
        </el-form-item>
        <el-form-item label="费率" prop="rate">
          <el-input v-model="setRateForm.rate" placeholder="请输入费率" />
        </el-form-item>
        <el-form-item label="类型" prop="paytype">
          <el-select v-model="setRateForm.paytype" placeholder="请选择类型">
            <el-option label="微信" value="huya" />
            <el-option label="支付宝" value="yahu" />
          </el-select>
        </el-form-item>
        <el-form-item label="成本" prop="cost">
          <el-input v-model="setRateForm.cost" placeholder="请输入成本" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="setRateDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitSetRate">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 预付编辑对话框 -->
    <el-dialog :title="'编辑预付'" :visible.sync="prepaidDialogVisible" width="90%" class="mobile-dialog">
      <el-form :model="prepaidForm" :rules="prepaidRules" ref="prepaidForm" label-width="90px">
        <el-form-item label="商户ID">
          <el-input v-model="prepaidForm.id" readonly />
        </el-form-item>
        <el-form-item label="商户名称">
          <el-input v-model="prepaidForm.name" readonly />
        </el-form-item>
        <el-form-item label="当前预付">
          <el-input v-model="prepaidForm.currentPrepaid" readonly />
        </el-form-item>
        <el-form-item label="当前余额">
          <el-input v-model="prepaidForm.prepaidBalance" readonly />
        </el-form-item>
        <el-form-item label="操作类型" prop="operationType">
          <el-radio-group v-model="prepaidForm.operationType">
            <el-radio label="increase">增加</el-radio>
            <el-radio label="decrease">减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="金额" prop="amount">
          <el-input v-model="prepaidForm.amount" placeholder="请输入金额" type="number">
            <template slot="append">元</template>
          </el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="prepaidForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="prepaidDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitPrepaid">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 商户详情对话框 -->
    <el-dialog :title="'商户详情'" :visible.sync="detailDialogVisible" width="95%" class="merchant-detail-dialog mobile-dialog">
      <div>
        <div class="merchant-base-info-card">
          <div class="info-row-flex">
            <div class="info-col id-col">
              <i class="el-icon-s-custom info-icon"></i>
              <span class="info-label">商户ID：</span>
              <span class="info-value">{{ detailData.id }}</span>
            </div>
            <div class="info-col name-col">
              <span class="info-label">商户名称：</span>
              <span class="info-value">{{ detailData.name }}</span>
            </div>
            <div class="info-col status-col">
              <span class="info-label">状态：</span>
              <el-tag :type="detailData.status === 1 ? 'success' : 'info'" size="mini">
                {{ detailData.status === 1 ? '开启' : '关闭' }}
              </el-tag>
            </div>
          </div>
          <div class="info-row-flex">
            <div class="info-col limit-col">
              <span class="info-label">最小金额：</span>
              <span class="info-value">{{ detailData.min_amount ? '¥' + detailData.min_amount : '未设置' }}</span>
            </div>
            <div class="info-col limit-col">
              <span class="info-label">最大金额：</span>
              <span class="info-value">{{ detailData.max_amount ? '¥' + detailData.max_amount : '未设置' }}</span>
            </div>
            <div class="info-col tg-col">
              <span class="info-label">TG群ID：</span>
              <span class="info-value">{{ detailData.telegram_group_id || '未设置' }}</span>
            </div>
          </div>
          <div class="info-divider"></div>
          <div class="info-item remark-row">
            <span class="info-label">备注：</span>
            <span class="info-value">{{ detailData.remark }}</span>
          </div>
        </div>
        <div class="merchant-statistics-area">
          <div class="paytype-switch">
            <el-button-group>
              <el-button :type="statisticsPaytype==='yahu'?'primary':'default'" @click="statisticsPaytype='yahu'">支付宝</el-button>
              <el-button :type="statisticsPaytype==='huya'?'primary':'default'" @click="statisticsPaytype='huya'">微信</el-button>
            </el-button-group>
          </div>
          <el-collapse v-if="currentPeriodsDatesList.length" v-model="statisticsActiveDates">
            <el-collapse-item v-for="date in currentPeriodsDatesList" :key="date" :title="date" :name="date">
              <el-table
                :data="currentPeriodsDates[date]"
                size="mini"
                border
                class="statistics-table"
                show-summary
                :summary-method="tableSummaryMethod"
                style="width: 100%; overflow-x: auto;"
              >
                <el-table-column prop="start_time" label="开始时间" min-width="120" />
                <el-table-column prop="end_time" label="结束时间" min-width="120" />
                <el-table-column prop="amount" label="跑量" min-width="100">
                  <template slot-scope="scope">
                    <span class="amount-cell">{{ scope.row.amount }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="rate" label="跑量费率" min-width="80">
                  <template slot-scope="scope">
                    <span class="rate-cell">{{ scope.row.rate }}%</span>
                  </template>
                </el-table-column>
                <el-table-column prop="cost" label="成本费率" min-width="100">
                  <template slot-scope="scope">
                    <span class="cost-cell">{{ scope.row.cost }}%</span>
                  </template>
                </el-table-column>
                <el-table-column prop="settle_amount" label="汇后入账" min-width="100">
                  <template slot-scope="scope">
                    <span class="settle-cell">{{ scope.row.settle_amount }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="profit" label="利润" min-width="100">
                  <template slot-scope="scope">
                    <span class="profit-cell">{{ scope.row.profit }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
          <div v-else class="no-data">暂无数据</div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMerchantList, updateMerchantStatus, deleteMerchant, addMerchant, updateMerchant, adjustPrepaid } from '@/api/merchant'
import { setRate, getMerchantDetail } from '@/api/merchant'

export default {
  name: 'Merchant',
  filters: {
    formatStatus(status) {
      const statusMap = {
        1: '开启',
        0: '关闭'
      }
      return statusMap[status] || status
    },
    formatKey(key) {
      if (!key) return '-'
      // 只显示前6位和后4位，中间用星号代替
      return key.length > 10 ? `${key.substring(0, 6)}****${key.substring(key.length - 4)}` : key
    },
    formatMoney(value) {
      if (value === undefined || value === null) return '¥0.00'
      return `¥${parseFloat(value).toFixed(2)}`
    },
    formatRate(rate) {
      if (rate === null || rate === undefined || rate === '') return '未设置'
      return rate
    }
  },
  data() {
    return {
      listQuery: {
        name: '',
        status: '',
        page: 1,
        limit: 50
      },
      statusOptions: [
        { key: 1, label: '开启' },
        { key: 0, label: '关闭' }
      ],
      tableData: [],
      displayData: [], // 用于显示的分组数据
      groupedData: {}, // 分组后的原始数据
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      dialogVisible: false,
      dialogTitle: '',
      temp: {
        name: '',
        key: '',
        status: 1,
        remark: '',
        min_amount: '',
        max_amount: '',
        telegram_group_id: ''
      },
      rules: {
        name: [{ required: true, message: '请输入商户名称', trigger: 'blur' }],
        key: [{ required: true, message: '请输入密钥', trigger: 'blur' }],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }],
        min_amount: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式' }
        ],
        max_amount: [
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式' },
          { validator: this.validateMaxAmount, trigger: 'blur' }
        ]
      },
      setRateDialogVisible: false,
      setRateForm: {
        id: '',
        name: '',
        rate: '',
        time: '',
        paytype: 'huya',
        cost: ''
      },
      setRateRules: {
        rate: [
          { required: true, message: '请输入费率', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的费率格式' }
        ]
      },
      detailDialogVisible: false,
      detailData: {},
      statisticsPaytype: 'yahu',
      statisticsActiveDates: [],
      prepaidDialogVisible: false,
      prepaidForm: {
        id: '',
        name: '',
        currentPrepaid: '',
        prepaidBalance: '',
        operationType: 'increase',
        amount: '',
        remark: ''
      },
      prepaidRules: {
        operationType: [
          { required: true, message: '请选择操作类型', trigger: 'change' }
        ],
        amount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getStatusLabel(status) {
      const statusMap = {
        1: '开启',
        0: '关闭'
      }
      return statusMap[status] || status
    },
    async getList() {
      this.loading = true
      try {
        getMerchantList(this.listQuery).then(response => {
          const { list, total, page, limit } = response.data
          this.tableData = list.map(item => ({
            ...item,
            statusLabel: this.getStatusLabel(item.status)
          }))
          this.total = total
          this.currentPage = page
          this.pageSize = limit

          // 处理分组数据
          this.processGroupedData()
        }).finally(() => {
          this.loading = false
        })
      } catch (error) {
        this.$message.error('获取数据失败')
        console.error('Error:', error)
        this.loading = false
      }
    },
    // 处理分组数据
    processGroupedData() {
      // 按分组ID分组商户
      const groups = {}
      const ungrouped = []

      this.tableData.forEach(merchant => {
        if (merchant.group_id && merchant.group_name) {
          if (!groups[merchant.group_id]) {
            groups[merchant.group_id] = {
              groupId: merchant.group_id,
              groupName: merchant.group_name,
              merchants: [],
              totalPrepaid: 0,
              totalBalance: 0
            }
          }
          groups[merchant.group_id].merchants.push(merchant)
          groups[merchant.group_id].totalPrepaid += parseFloat(merchant.current_prepaid || 0)
          groups[merchant.group_id].totalBalance += parseFloat(merchant.prepaid_balance || 0)
        } else {
          ungrouped.push(merchant)
        }
      })

      this.groupedData = groups

      // 构建显示数据
      const displayData = []

      // 添加分组数据
      Object.values(groups).forEach(group => {
        group.merchants.forEach((merchant, index) => {
          displayData.push({
            ...merchant,
            isGroupHeader: index === 0,
            groupName: group.groupName,
            groupSize: group.merchants.length,
            groupTotalPrepaid: group.totalPrepaid,
            groupTotalBalance: group.totalBalance,
            rowIndex: index
          })
        })
      })

      // 添加未分组数据 - 每个未分组商户都作为独立的分组头部
      ungrouped.forEach(merchant => {
        displayData.push({
          ...merchant,
          isGroupHeader: true, // 未分组商户也作为分组头部，这样可以显示合计
          groupName: '未分组',
          groupSize: 1,
          groupTotalPrepaid: parseFloat(merchant.current_prepaid || 0),
          groupTotalBalance: parseFloat(merchant.prepaid_balance || 0),
          rowIndex: 0
        })
      })

      this.displayData = displayData
    },
    // 表格合并单元格方法
    spanMethod({ row, columnIndex }) {
      // 分组名称列合并 (第0列)
      if (columnIndex === 0) {
        if (row.isGroupHeader) {
          return {
            rowspan: row.groupSize,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      // 分组合计列合并 (分组合计列是第5列，索引为4)
      if (columnIndex === 4) {
        if (row.isGroupHeader) {
          return {
            rowspan: row.groupSize,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
      return {
        rowspan: 1,
        colspan: 1
      }
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleAdd() {
      this.dialogTitle = '新增商户'
      this.temp = {
        name: '',
        key: '',
        status: 1,
        remark: '',
        min_amount: '',
        max_amount: '',
        telegram_group_id: ''
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleEdit(row) {
      this.dialogTitle = '编辑商户'
      this.temp = {
        id: row.id,
        name: row.name,
        key: row.key,
        status: row.status,
        remark: row.remark || '',
        min_amount: row.min_amount || '',
        max_amount: row.max_amount || '',
        telegram_group_id: row.telegram_group_id || ''
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该商户吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        deleteMerchant({ id: row.id }).then(response => {
          this.$message.success('删除成功')
          this.getList()
        })
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error('Error:', error)
        }
      }
    },
    async handleStatusChange(row) {
      updateMerchantStatus({
        id: row.id,
        status: row.status
      }).then(response => {
        this.$message.success(`商户 ${row.name} 状态已更新`)
      })
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },
    async saveData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            const isNew = this.dialogTitle === '新增商户'
            const apiMethod = isNew ? addMerchant : updateMerchant

            const response = await apiMethod(this.temp)

            if (response.code === 1) {
              this.$message.success(isNew ? '新增成功' : '编辑成功')
              this.dialogVisible = false
              this.getList()
            } else {
              this.$message.error(response.msg || (isNew ? '新增失败' : '编辑失败'))
            }
          } catch (error) {
            this.$message.error(this.dialogTitle === '新增商户' ? '新增失败' : '编辑失败')
            console.error('Error:', error)
          }
        }
      })
    },
    // 生成随机密钥
    generateKey() {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      const length = 32
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length))
      }
      this.temp.key = result
    },
    // 验证最大金额
    validateMaxAmount(rule, value, callback) {
      if (value && this.temp.min_amount) {
        const max = parseFloat(value)
        const min = parseFloat(this.temp.min_amount)
        if (max <= min) {
          callback(new Error('最大金额必须大于最小金额'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    },
    // 格式化限额范围显示
    formatLimitRange(minAmount, maxAmount) {
      if (!minAmount && !maxAmount) {
        return '未设置'
      }

      const min = minAmount ? parseFloat(minAmount).toFixed(2) : '0.00'
      const max = maxAmount ? parseFloat(maxAmount).toFixed(2) : '∞'

      if (min === '0.00' && max === '∞') {
        return '无限制'
      } else if (min === '0.00') {
        return `≤${max}`
      } else if (max === '∞') {
        return `≥${min}`
      } else {
        return `${min}-${max}`
      }
    },
    // 复制密钥到剪贴板

    copyKey(key) {
      const textarea = document.createElement('textarea')
      textarea.value = key
      document.body.appendChild(textarea)
      textarea.select()
      document.execCommand('copy')
      document.body.removeChild(textarea)
      this.$message.success('密钥已复制到剪贴板')
    },
    async handleSetRate(row) {
      this.setRateForm = {
        id: row.id,
        name: row.name,
        rate: row.rate,
        time: this.setRateForm && this.setRateForm.time ? this.setRateForm.time : Date.now(),
        paytype: 'huya',
        cost: ''
      }
      this.setRateDialogVisible = true
    },
    formatNow() {
      const now = new Date()
      const yyyy = now.getFullYear()
      const mm = String(now.getMonth() + 1).padStart(2, '0')
      const dd = String(now.getDate()).padStart(2, '0')
      const hh = String(now.getHours()).padStart(2, '0')
      const mi = String(now.getMinutes()).padStart(2, '0')
      const ss = String(now.getSeconds()).padStart(2, '0')
      return `${yyyy}-${mm}-${dd} ${hh}:${mi}:${ss}`
    },
    async handleDetail(row) {
      try {
        const res = await getMerchantDetail({ id: row.id })
        if (res.code === 1) {
          this.detailData = res.data
          this.detailDialogVisible = true
          // 默认展示当天支付宝数据
          this.statisticsPaytype = 'yahu'
          const yahuPeriods = this.detailData.rate_periods.yahu || {}
          if (Object.keys(yahuPeriods).length) {
            // 新的数据结构已经按日期分组，直接获取日期列表
            const dates = Object.keys(yahuPeriods).sort().reverse()
            if (dates.length) {
              this.statisticsActiveDates = [dates[0]]
            } else {
              this.statisticsActiveDates = []
            }
          } else {
            this.statisticsActiveDates = []
          }
        } else {
          this.$message.error(res.msg || '获取详情失败')
        }
      } catch (e) {
        this.$message.error('获取详情失败')
      }
    },
    async submitSetRate() {
      this.$refs.setRateForm.validate(async(valid) => {
        if (!valid) return
        try {
          const payload = {
            id: this.setRateForm.id,
            rate: this.setRateForm.rate,
            time: Math.floor(this.setRateForm.time / 1000), // 秒级时间戳
            paytype: this.setRateForm.paytype,
            cost: this.setRateForm.cost
          }
          const res = await setRate(payload)
          if (res.code === 1) {
            this.$message.success('费率设置成功')
            this.setRateDialogVisible = false
            this.getList()
          } else {
            this.$message.error(res.msg || '费率设置失败')
          }
        } catch (e) {
          this.$message.error('费率设置失败')
        }
      })
    },
    tableSummaryMethod({ columns, data }) {
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (['amount', 'settle_amount', 'profit'].includes(column.property)) {
          const total = data.reduce((sum, row) => {
            const val = parseFloat(row[column.property])
            return sum + (isNaN(val) ? 0 : val)
          }, 0)
          sums[index] = total.toFixed(2)
        } else {
          sums[index] = ''
        }
      })
      return sums
    },
    handleEditPrepaid(row) {
      this.prepaidForm = {
        id: row.id,
        name: row.name,
        currentPrepaid: row.current_prepaid || '0.00',
        prepaidBalance: row.prepaid_balance || '0.00',
        operationType: 'increase',
        amount: '',
        remark: ''
      }
      this.prepaidDialogVisible = true
      this.$nextTick(() => {
        this.$refs['prepaidForm'].clearValidate()
      })
    },
    async submitPrepaid() {
      this.$refs.prepaidForm.validate(async(valid) => {
        if (!valid) return
        try {
          // 根据操作类型计算金额（增加为正数，减少为负数）
          const amount = this.prepaidForm.operationType === 'increase'
            ? parseFloat(this.prepaidForm.amount)
            : -parseFloat(this.prepaidForm.amount)

          const payload = {
            merchant_id: this.prepaidForm.id,
            amount: amount,
            remark: this.prepaidForm.remark
          }

          const res = await adjustPrepaid(payload)
          if (res.code === 1) {
            this.$message.success('预付更新成功')
            this.prepaidDialogVisible = false
            this.getList()
          } else {
            this.$message.error(res.msg || '预付更新失败')
          }
        } catch (e) {
          this.$message.error('预付更新失败')
          console.error('Error:', e)
        }
      })
    }
  },
  watch: {
    detailDialogVisible(val) {
      if (val && this.detailData && this.detailData.rate_periods) {
        // 默认展示当天支付宝数据
        this.statisticsPaytype = 'yahu'
        const yahuPeriods = this.detailData.rate_periods.yahu || {}
        if (Object.keys(yahuPeriods).length) {
          // 新的数据结构已经按日期分组，直接获取日期列表
          const dates = Object.keys(yahuPeriods).sort().reverse()
          if (dates.length) {
            this.statisticsActiveDates = [dates[0]]
          } else {
            this.statisticsActiveDates = []
          }
        } else {
          this.statisticsActiveDates = []
        }
      }
    },
    statisticsPaytype() {
      // 切换支付类型时，默认展开最新日期
      this.$nextTick(() => {
        const dates = this.currentPeriodsDatesList
        if (dates.length) {
          this.statisticsActiveDates = [dates[0]]
        } else {
          this.statisticsActiveDates = []
        }
      })
    }
  },
  computed: {
    currentPeriods() {
      if (!this.detailData.rate_periods) return {}
      const periods = this.detailData.rate_periods[this.statisticsPaytype] || {}
      return periods
    },
    currentPeriodsDates() {
      // 新的数据结构中，rate_periods[paytype]是一个对象，按日期分组
      if (!this.detailData.rate_periods || !this.detailData.rate_periods[this.statisticsPaytype]) return {}

      const periods = this.detailData.rate_periods[this.statisticsPaytype]

      // 直接返回按日期分组的对象
      return periods
    },
    currentPeriodsDatesList() {
      // 返回日期列表，用于展开/收起控制
      return Object.keys(this.currentPeriodsDates).sort().reverse()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  @media screen and (max-width: 768px) {
    padding: 10px;
  }
}

.filter-container {
  padding-bottom: 20px;

  @media screen and (max-width: 768px) {
    padding-bottom: 15px;
  }

  .filter-row {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 10px;

    .filter-item {
      margin-right: 10px;
      margin-bottom: 0;
      &:last-child {
        margin-right: 0;
      }
    }

    @media screen and (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      .filter-item {
        margin-right: 0;
        margin-bottom: 8px;
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .search-input {
    width: 200px;

    @media screen and (max-width: 768px) {
      width: 100%;
    }
  }

  .status-select {
    width: 130px;

    @media screen and (max-width: 768px) {
      width: 100%;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;

  @media screen and (max-width: 768px) {
    margin-top: 15px;
    text-align: center;

    ::v-deep .el-pagination {
      text-align: center;
      white-space: normal;

      .el-pagination__total,
      .el-pagination__sizes,
      .el-pagination__jump {
        margin: 5px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;

  @media screen and (max-width: 768px) {
    text-align: center;

    .el-button {
      margin: 0 5px;
    }
  }
}

// 移动端对话框样式
.mobile-dialog {
  @media screen and (max-width: 768px) {
    ::v-deep .el-dialog {
      margin: 5vh auto !important;
      max-width: 95vw;

      .el-dialog__body {
        padding: 15px;
        max-height: 70vh;
        overflow-y: auto;
      }

      .el-dialog__header {
        padding: 15px 20px 10px;
      }

      .el-dialog__footer {
        padding: 10px 20px 15px;
      }

      // 移动端表单优化
      .el-form {
        .el-form-item {
          margin-bottom: 15px;

          .el-form-item__label {
            font-size: 14px;
            line-height: 1.4;
          }

          .el-form-item__content {
            .el-input,
            .el-select {
              .el-input__inner {
                height: 36px;
                line-height: 36px;
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }
}

::v-deep .el-table th {
  background-color: #f5f7fa;
}

// 移动端表格优化
@media screen and (max-width: 768px) {
  .merchant-table {
    overflow-x: auto;
    min-width: 1200px;

    &::-webkit-scrollbar {
      height: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
    }
  }

  ::v-deep .el-table {
    font-size: 12px;

    .el-table__header th {
      padding: 8px 4px;
      font-size: 12px;
    }

    .el-table__body td {
      padding: 6px 4px;
      font-size: 12px;
    }

    .el-table__row {
      height: auto;
      min-height: 40px;
    }

    // 移除固定列的样式，确保可以正常滑动
    .el-table__fixed-right {
      position: static !important;
      box-shadow: none !important;
    }

    .el-table__fixed-right-patch {
      display: none !important;
    }

    .el-table__body-wrapper {
      overflow-x: auto !important;
    }
  }
}

::v-deep .el-tag {
  text-transform: capitalize;
}

// 移动端开关优化
@media screen and (max-width: 768px) {
  ::v-deep .el-switch {
    .el-switch__core {
      width: 40px;
      height: 20px;
    }

    .el-switch__core:after {
      width: 16px;
      height: 16px;
      top: 1px;
    }
  }

  // 移动端触摸优化
  .el-button {
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
  }

  .el-input__inner,
  .el-select .el-input__inner {
    -webkit-tap-highlight-color: transparent;
  }

  // 移动端滚动优化
  .el-table__body-wrapper {
    -webkit-overflow-scrolling: touch;
  }
}

.operation-btn {
  padding: 4px 8px;
  margin: 0 2px;
  font-size: 12px;
  min-width: 88px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  [class^="el-icon-"] {
    font-size: 12px;
    margin-right: 2px;
  }

    @media screen and (max-width: 768px) {
    padding: 6px 8px;
    font-size: 12px;
    min-width: 70px;
    margin: 0 2px;
    min-height: 32px;

    [class^="el-icon-"] {
      font-size: 12px;
      margin-right: 2px;
    }
  }
}

.secret-key {
  font-family: monospace;
  letter-spacing: 1px;
  cursor: pointer;

  @media screen and (max-width: 768px) {
    font-size: 11px;
    letter-spacing: 0.5px;
  }
}

.copy-btn {
  margin-left: 5px;
  font-size: 14px;

  &:hover {
    color: #409EFF;
  }

  @media screen and (max-width: 768px) {
    margin-left: 3px;
    font-size: 12px;
  }
}

.amount-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 4px 0;

  @media screen and (max-width: 768px) {
    gap: 2px;
    padding: 2px 0;
  }
}

.amount-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  @media screen and (max-width: 768px) {
    gap: 2px;
  }
}

.amount-label {
  color: #606266;
  font-size: 13px;

  @media screen and (max-width: 768px) {
    font-size: 11px;
  }
}

.amount-value {
  font-weight: bold;
  color: #67C23A;
  font-family: monospace;

  @media screen and (max-width: 768px) {
    font-size: 11px;
  }
}

.btn-margin {
  margin-right: 8px;

  @media screen and (max-width: 768px) {
    margin-right: 4px;
  }
}

.operation-btns {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 4px;

  @media screen and (max-width: 768px) {
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 2px;
    min-width: 200px;
  }
}

.merchant-detail-dialog .el-descriptions {
  margin-bottom: 24px;
  font-size: 15px;
  .el-descriptions-item__label {
    font-weight: bold;
    color: #333;
    min-width: 90px;
  }
  .el-descriptions-item__content {
    color: #555;
    word-break: break-all;
  }
}

.merchant-detail-dialog .el-tabs__content {
  padding: 16px 8px 0 8px;
  background: #fafbfc;
  border-radius: 4px;
}

.merchant-detail-dialog .el-collapse-item__header {
  font-weight: bold;
}

.merchant-detail-dialog .el-table {
  background: #fff;
}

.merchant-base-info-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 24px 32px 12px 32px;
  margin-bottom: 28px;

  @media screen and (max-width: 768px) {
    padding: 16px 20px 8px 20px;
    margin-bottom: 20px;
  }

  .el-row {
    margin-bottom: 0;
  }
  .info-row-flex {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 32px;
    margin-bottom: 10px;

    @media screen and (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
      margin-bottom: 8px;
    }

    .info-col {
      display: flex;
      align-items: center;
      min-width: 180px;
      font-size: 15px;

      @media screen and (max-width: 768px) {
        min-width: auto;
        width: 100%;
        font-size: 14px;
      }
    }
    .id-col {
      min-width: 200px;

      @media screen and (max-width: 768px) {
        min-width: auto;
      }
    }
    .name-col {
      min-width: 220px;

      @media screen and (max-width: 768px) {
        min-width: auto;
      }
    }
    .status-col {
      min-width: 120px;
      justify-content: flex-end;
      flex: 1;

      @media screen and (max-width: 768px) {
        justify-content: flex-start;
        flex: none;
      }
    }
    .limit-col {
      min-width: 180px;

      @media screen and (max-width: 768px) {
        min-width: auto;
        width: 100%;
        font-size: 14px;
      }
    }
    .info-label {
      font-weight: 500;
      color: #222;
      min-width: 70px;
      margin-right: 6px;

      @media screen and (max-width: 768px) {
        min-width: 60px;
        font-size: 13px;
      }
    }
    .info-value {
      color: #555;
      word-break: break-all;

      @media screen and (max-width: 768px) {
        font-size: 13px;
      }
    }
    .info-icon {
      color: #409EFF;
      font-size: 18px;
      margin-right: 6px;

      @media screen and (max-width: 768px) {
        font-size: 16px;
      }
    }
  }
  .info-divider {
    border-bottom: 1px solid #f0f0f0;
    margin: 6px 0 12px 0;

    @media screen and (max-width: 768px) {
      margin: 4px 0 8px 0;
    }
  }
  .remark-row {
    font-size: 14px;
    color: #888;
    margin-bottom: 0;

    @media screen and (max-width: 768px) {
      font-size: 13px;
    }
  }
}

.merchant-statistics-area {
  background: #f8f9fb;
  border-radius: 8px;
  padding: 18px 18px 8px 18px;
  margin-bottom: 12px;

  @media screen and (max-width: 768px) {
    padding: 12px 12px 6px 12px;
    margin-bottom: 8px;
  }

  .paytype-switch {
    margin-bottom: 18px;
    text-align: center;

    @media screen and (max-width: 768px) {
      margin-bottom: 12px;

      .el-button-group {
        .el-button {
          padding: 6px 12px;
          font-size: 12px;
        }
      }
    }
  }
  .statistics-table {
    background: #fff;
    border-radius: 4px;
    margin-bottom: 8px;

    @media screen and (max-width: 768px) {
      font-size: 11px;

      th {
        padding: 6px 4px;
        font-size: 11px;
      }

      td {
        padding: 4px 2px;
        font-size: 11px;
      }
    }

    th {
      background: linear-gradient(90deg, #f0f2f5 60%, #e6f7ff 100%);
      font-weight: bold;
      color: #222;
      font-size: 15px;
      letter-spacing: 1px;
      border-bottom: 2px solid #91d5ff;
    }
    td {
      color: #444;
      font-size: 14px;
    }
    .amount-cell {
      color: #409EFF;
      font-weight: 500;
    }
    .rate-cell {
      color: #1890ff;
      font-weight: 500;
    }
    .cost-cell {
      color: #fa8c16;
      font-weight: 500;
    }
    .settle-cell {
      color: #52c41a;
      font-weight: 500;
    }
    .profit-cell {
      color: #f5222d;
      font-weight: bold;
    }
  }
  .no-data {
    color: #aaa;
    text-align: center;
    padding: 32px 0;

    @media screen and (max-width: 768px) {
      padding: 20px 0;
      font-size: 13px;
    }
  }
}

.settle-amount-card {
  background: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 6px;
  padding: 14px 28px;
  margin-bottom: 18px;
  font-size: 18px;
  font-weight: bold;
  color: #d48806;
  display: flex;
  align-items: center;
  .settle-label {
    margin-right: 12px;
    font-size: 16px;
    color: #ad6800;
    font-weight: 500;
  }
  .settle-value {
    font-size: 20px;
    color: #d48806;
    font-weight: bold;
  }
}

.table-scroll-x {
  width: 100%;
  overflow-x: auto;

  @media screen and (max-width: 768px) {
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;

    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c0c4cc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: #f5f7fa;
    }
  }
}

.merchant-table {
  min-width: 900px;

  @media screen and (max-width: 768px) {
    min-width: 1200px;
  }
}

.prepaid-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  @media screen and (max-width: 768px) {
    gap: 4px;
  }
}

.prepaid-value {
  font-weight: 500;
  color: #409EFF;
}

.balance-value {
  font-weight: 500;
  color: #67C23A;
  font-family: monospace;
}

.negative-balance {
  color: #F56C6C !important;
}

.limit-range {
  font-family: monospace;
  font-weight: 500;
  color: #409EFF;
  font-size: 13px;

  @media screen and (max-width: 768px) {
    font-size: 11px;
  }
}

.edit-prepaid-btn {
  padding: 2px;
  font-size: 14px;
  color: #909399;
  transition: all 0.3s;

  &:hover {
    color: #409EFF;
    transform: scale(1.1);
  }

  @media screen and (max-width: 768px) {
    font-size: 12px;
    padding: 1px;
  }
}

// 分组相关样式
.group-name {
  font-weight: bold;
  color: #409EFF;
  background: #f0f9ff;
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 4px solid #409EFF;
  font-size: 14px;
  text-align: center;

  &.ungrouped {
    color: #6c757d;
    background: #f8f9fa;
    border-left-color: #6c757d;
    font-style: italic;
  }
}

.group-summary {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.group-summary-card {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 10px 8px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0,0,0,0.08);
  min-width: 180px;
  width: 100%;
  box-sizing: border-box;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  padding: 2px 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
  white-space: nowrap;

  &:last-child {
    margin-bottom: 0;
  }

  &:hover {
    background-color: rgba(255,255,255,0.5);
  }
}

.summary-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 600;
  min-width: 32px;
  text-align: left;
  flex-shrink: 0;
}

.summary-value {
  font-weight: bold;
  font-size: 12px;
  text-align: right;
  white-space: nowrap;
  flex: 1;
  margin-left: 8px;
  min-width: 0; // 允许flex收缩

  &.prepaid {
    color: #28a745;
  }

  &.balance {
    color: #007bff;

    &.negative-balance {
      color: #dc3545;
    }
  }
}

.group-total {
  font-weight: bold;
  color: #409EFF;
}

.group-summary-empty {
  height: 100%;
  min-height: 60px;
}

/* 个人信息保持原有样式 */

.tg-group-id {
  font-family: monospace;
  font-weight: 500;
  color: #409EFF;
  background: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 13px;

  @media screen and (max-width: 768px) {
    font-size: 11px;
    padding: 1px 4px;
  }
}

.no-tg-group {
  color: #909399;
  font-style: italic;
  font-size: 13px;

  @media screen and (max-width: 768px) {
    font-size: 11px;
  }
}

.tg-col {
  min-width: 180px;

  @media screen and (max-width: 768px) {
    min-width: auto;
    width: 100%;
    font-size: 14px;
  }
}

.merchant-info-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 4px 0;

  @media screen and (max-width: 768px) {
    gap: 4px;
    padding: 2px 0;
  }
}

.merchant-name {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
  text-align: center;
  word-break: break-all;
  max-width: 100%;

  @media screen and (max-width: 768px) {
    font-size: 13px;
  }
}

.merchant-key-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  @media screen and (max-width: 768px) {
    gap: 2px;
  }
}

.account-info-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 4px 0;

  @media screen and (max-width: 768px) {
    gap: 4px;
    padding: 2px 0;
  }
}

.prepaid-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  @media screen and (max-width: 768px) {
    gap: 2px;
  }
}

.balance-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;

  @media screen and (max-width: 768px) {
    gap: 2px;
  }
}

.prepaid-label,
.balance-label {
  color: #606266;
  font-size: 12px;
  min-width: 35px;

  @media screen and (max-width: 768px) {
    font-size: 11px;
    min-width: 30px;
  }
}
</style>
