<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="filter-container">
        <div class="filter-row">
          <el-input
            v-model="listQuery.name"
            placeholder="分组名称"
            class="filter-item search-input"
            @keyup.enter.native="handleFilter"
          />
          <el-select
            v-model="listQuery.status"
            placeholder="状态"
            clearable
            class="filter-item status-select"
          >
            <el-option
              v-for="item in statusOptions"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            搜索
          </el-button>
          <el-button
            class="filter-item"
            type="success"
            icon="el-icon-plus"
            @click="handleAdd"
          >
            新增
          </el-button>
        </div>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        fit
        highlight-current-row
        style="width: 100%;"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="name" label="分组名称" min-width="150" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ getStatusLabel(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" min-width="200" />
        <el-table-column label="绑定商户" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ getMerchantCount(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createtime" label="创建时间" width="180" align="center">
          <template slot-scope="scope">
            {{ formatTimestamp(scope.row.createtime) }}
          </template>
        </el-table-column>
        <el-table-column prop="updatetime" label="更新时间" width="180" align="center">
          <template slot-scope="scope">
            {{ formatTimestamp(scope.row.updatetime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="resetForm"
    >
      <el-form
        ref="dataForm"
        :model="temp"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item label="分组名称" prop="name">
          <el-input
            v-model="temp.name"
            placeholder="请输入分组名称"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="temp.status" placeholder="请选择状态">
            <el-option
              v-for="item in statusOptions"
              :key="item.key"
              :label="item.label"
              :value="item.key"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="绑定商户" prop="merchant_ids">
          <el-select
            v-model="temp.merchant_ids"
            multiple
            filterable
            placeholder="请选择要绑定的商户"
            style="width: 100%"
          >
            <el-option
              v-for="merchant in merchantOptions"
              :key="merchant.id"
              :label="merchant.name"
              :value="merchant.id"
            />
          </el-select>
          <div class="form-tip">可选择多个商户绑定到此分组</div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="temp.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveData">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getMerchantGroupList, deleteMerchantGroup, addMerchantGroup, updateMerchantGroup, getMerchantGroupDetail } from '@/api/merchant_group'
import { getMerchantList } from '@/api/merchant'

export default {
  name: 'MerchantGroup',
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      listQuery: {
        page: 1,
        limit: 10,
        name: '',
        status: ''
      },
      statusOptions: [
        { key: 1, label: '启用' },
        { key: 0, label: '禁用' }
      ],
      dialogVisible: false,
      dialogTitle: '',
      temp: {
        id: null,
        name: '',
        status: 1,
        remark: '',
        merchant_ids: []
      },
      merchantOptions: [],
      rules: {
        name: [
          { required: true, message: '请输入分组名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  created() {
    this.getList()
    this.getMerchantOptions()
  },
  methods: {
    getStatusLabel(status) {
      const statusMap = {
        1: '启用',
        0: '禁用'
      }
      return statusMap[status] || status
    },
    formatTimestamp(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp * 1000)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    },
    async getList() {
      this.loading = true
      try {
        const response = await getMerchantGroupList(this.listQuery)
        const { list, total, page, limit } = response.data
        this.tableData = list
        this.total = total
        this.currentPage = page
        this.pageSize = limit
      } catch (error) {
        this.$message.error('获取数据失败')
        console.error('Error:', error)
      } finally {
        this.loading = false
      }
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    getMerchantCount(row) {
      if (row.merchant_count !== undefined) {
        return `${row.merchant_count}个`
      }
      if (row.merchants && Array.isArray(row.merchants)) {
        return `${row.merchants.length}个`
      }
      if (row.merchant_ids && Array.isArray(row.merchant_ids)) {
        return `${row.merchant_ids.length}个`
      }
      return '0个'
    },
    async getMerchantOptions() {
      try {
        const response = await getMerchantList({ page: 1, limit: 1000 })
        this.merchantOptions = response.data.list || []
      } catch (error) {
        console.error('获取商户列表失败:', error)
      }
    },
    handleAdd() {
      this.dialogTitle = '新增分组'
      this.temp = {
        id: null,
        name: '',
        status: 1,
        remark: '',
        merchant_ids: []
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleEdit(row) {
      this.dialogTitle = '编辑分组'

      // 先设置基本信息
      this.temp = {
        id: row.id,
        name: row.name,
        status: row.status,
        remark: row.remark || '',
        merchant_ids: []
      }

      // 获取分组详情，尝试获取已绑定的商户
      try {
        const response = await getMerchantGroupDetail({ id: row.id })
        const detail = response.data

        console.log('分组详情数据:', detail) // 调试信息

        // 更新基本信息（以详情接口为准）
        this.temp.name = detail.name || row.name
        this.temp.status = detail.status !== undefined ? detail.status : row.status
        this.temp.remark = detail.remark || ''

        // 尝试获取商户信息（可能在详情中，也可能需要单独接口）
        if (detail.merchant_ids) {
          this.temp.merchant_ids = Array.isArray(detail.merchant_ids) ? detail.merchant_ids : []
          console.log('从详情获取到商户IDs:', this.temp.merchant_ids)
        } else if (detail.merchants) {
          // 如果返回的是商户对象数组，提取ID
          this.temp.merchant_ids = detail.merchants.map(m => m.id)
          console.log('从商户对象数组提取IDs:', this.temp.merchant_ids)
        } else {
          // 如果详情接口没有商户信息，保持为空数组
          this.temp.merchant_ids = []
          console.log('详情接口未返回商户信息，设置为空数组')
        }
      } catch (error) {
        console.error('获取分组详情失败:', error)
        // 如果获取详情失败，使用列表中的基本信息
        this.temp.merchant_ids = []
      }

      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该分组吗？删除后该分组下的商户将解除绑定。', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await deleteMerchantGroup({ id: row.id })
        this.$message.success('删除成功')
        this.getList()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error('Error:', error)
        }
      }
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },
    resetForm() {
      this.temp = {
        id: null,
        name: '',
        status: 1,
        remark: '',
        merchant_ids: []
      }
      if (this.$refs['dataForm']) {
        this.$refs['dataForm'].clearValidate()
      }
    },
    async saveData() {
      try {
        await this.$refs['dataForm'].validate()

        if (this.temp.id) {
          // 编辑
          await updateMerchantGroup(this.temp)
          this.$message.success('更新成功')
        } else {
          // 新增
          await addMerchantGroup(this.temp)
          this.$message.success('新增成功')
        }

        this.dialogVisible = false
        this.getList()
      } catch (error) {
        if (error !== false) { // 表单验证失败时会返回false
          this.$message.error(this.temp.id ? '更新失败' : '新增失败')
          console.error('Error:', error)
        }
      }
    }
  }
}
</script>

<style scoped>
.filter-container {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.search-input {
  width: 200px;
}

.status-select {
  width: 120px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.form-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}
</style>
