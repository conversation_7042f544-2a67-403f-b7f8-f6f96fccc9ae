<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-card class="filter-container" shadow="never">
      <el-form :inline="true" :model="listQuery" size="small" label-width="100px">
        <el-form-item label="订单号">
          <div class="order-no-search">
            <el-input
              v-model.trim="listQuery.out_trade_no"
              placeholder="商户订单号"
              clearable
              @input="debounceSearch"
              style="width: 180px;"
            />
          </div>
        </el-form-item>
        <el-form-item label="支付单号">
          <el-input
            v-model.trim="listQuery.dy_order_id"
            placeholder="支付单号"
            clearable
            style="width: 180px;"
          />
        </el-form-item>
        <el-form-item label="支付状态">
          <el-select v-model="listQuery.pay_status" placeholder="请选择" clearable style="width: 100px;">
            <el-option v-for="item in payStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="回调状态">
          <el-select v-model="listQuery.callback_status" placeholder="请选择" clearable style="width: 100px;">
            <el-option v-for="item in callbackStatusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="通道编码">
          <el-input
            v-model.trim="listQuery.paytype"
            placeholder="请输入通道编码"
            clearable
            style="width: 120px;"
          />
        </el-form-item>
        <el-form-item label="商户名称">
          <el-input
            v-model.trim="listQuery.merchant_name"
            placeholder="请输入商户名称"
            clearable
            style="width: 150px;"
          />
        </el-form-item>
        <el-form-item label="金额区间">
          <div class="amount-range-container">
            <el-input
              v-model.trim="listQuery.amount_min"
              placeholder="最小金额"
              clearable
              style="width: 120px;"
            />
            <span class="range-separator">至</span>
            <el-input
              v-model.trim="listQuery.amount_max"
              placeholder="最大金额"
              clearable
              style="width: 120px;"
            />
          </div>
        </el-form-item>
        <el-form-item label="创建时间">
          <div class="date-filter-container">
            <el-date-picker
              v-model="dateRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="pickerOptions"
              style="width: 380px;"
            />
            <div class="quick-date-filters">
              <el-button size="mini" type="text" @click="setQuickDateRange('hour')">最近1小时</el-button>
              <el-button size="mini" type="text" @click="setQuickDateRange('today')">今天</el-button>
              <el-button size="mini" type="text" @click="setQuickDateRange('yesterday')">昨天</el-button>
              <el-button size="mini" type="text" @click="setQuickDateRange('week')">最近一周</el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" :loading="searchLoading" @click="handleSearch">查询</el-button>
          <el-button icon="el-icon-refresh" :loading="searchLoading" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-container" shadow="never">
      <div slot="header" class="clearfix">
        <span>订单列表</span>
        <div style="float: right;">
          <el-popover
            placement="bottom"
            width="200"
            trigger="click"
            popper-class="column-filter-popover"
          >
            <el-checkbox
              v-model="checkAll"
              @change="handleCheckAllChange"
            >全选</el-checkbox>
            <div class="column-filter-list">
              <el-checkbox-group v-model="checkedColumns" @change="handleCheckedColumnsChange">
                <el-checkbox v-for="col in columnOptions" :key="col.prop" :label="col.prop">
                  {{ col.label }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
            <el-button
              slot="reference"
              size="mini"
              type="primary"
              plain
              icon="el-icon-s-operation"
              style="margin-left: 10px;"
            >列设置</el-button>
          </el-popover>
          <el-button
            size="mini"
            type="success"
            plain
            icon="el-icon-download"
            style="margin-left: 10px;"
            :loading="exportLoading"
            @click="handleExport"
          >导出</el-button>
          <el-button
            size="mini"
            type="primary"
            plain
            icon="el-icon-refresh-right"
            style="margin-left: 10px;"
            :loading="refreshLoading"
            @click="handleRefresh"
          >刷新</el-button>
        </div>
      </div>
      <el-table
        v-loading="listLoading"
        :data="list"
        element-loading-text="加载中..."
        border
        fit
        highlight-current-row
        style="width: 100%"
        size="mini"
        class="compact-table"
        @selection-change="handleSelectionChange"
        :row-class-name="tableRowClassName"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="订单号" min-width="220" align="" v-if="isColumnVisible('out_trade_no')">
          <template slot-scope="scope">
            <div class="order-no-column">
              <div class="order-no-item">
                <!-- <span class="label">商户单号:</span> -->
                <span class="value copy-value">
                  {{ scope.row.out_trade_no }}
                  <el-tooltip content="复制单号" placement="top" effect="light">
                    <i class="el-icon-document-copy copy-icon" @click="copyToClipboard(scope.row.out_trade_no)"></i>
                  </el-tooltip>
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="支付单号" min-width="180" align="" v-if="isColumnVisible('dy_order_id')">
          <template slot-scope="scope">
            <div class="order-no-column">
              <div class="order-no-item">
                <span class="value copy-value">
                  {{ scope.row.dy_order_id || '暂无' }}
                  <el-tooltip content="复制单号" placement="top" effect="light" v-if="scope.row.dy_order_id">
                    <i class="el-icon-document-copy copy-icon" @click="copyToClipboard(scope.row.dy_order_id)"></i>
                  </el-tooltip>
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="account_name" label="收款账号" min-width="120" align="center" v-if="isColumnVisible('account_name')" />
        <el-table-column prop="amount" label="收款金额" min-width="90" align="center" v-if="isColumnVisible('amount')">
          <template slot-scope="scope">
            <span class="amount-value">¥{{ scope.row.amount }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="paytype" label="通道编码" min-width="80" align="center" v-if="isColumnVisible('paytype')">
          <template slot-scope="scope">
            <el-tag size="mini">{{ scope.row.paytype }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="pay_status" label="支付状态" min-width="80" align="center" v-if="isColumnVisible('pay_status')">
          <template slot-scope="scope">
            <el-tag size="mini" :type="scope.row.pay_status | payStatusFilter">
              {{ scope.row.pay_status | payStatusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="callback_status" label="回调状态" min-width="80" align="center" v-if="isColumnVisible('callback_status')">
          <template slot-scope="scope">
            <el-tag size="mini" :type="scope.row.callback_status | callbackStatusFilter">
              {{ scope.row.callback_status | callbackStatusText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="callback_time" label="回调时间" min-width="140" align="center" class-name="time-column" v-if="isColumnVisible('callback_time')">
          <template slot-scope="scope">
            {{ scope.row.callback_time | formatTimestamp }}
          </template>
        </el-table-column>
        <el-table-column prop="create_time" label="创建时间" min-width="140" align="center" class-name="time-column" v-if="isColumnVisible('create_time')">
          <template slot-scope="scope">
            {{ scope.row.create_time | formatTimestamp }}
          </template>
        </el-table-column>
        <el-table-column prop="merchant_name" label="商户" min-width="100" align="center" v-if="isColumnVisible('merchant_name')" />
        <el-table-column prop="user_nickname" label="核销" min-width="100" align="center" v-if="isColumnVisible('user_nickname')" />
        <el-table-column prop="remark" label="备注" min-width="120" align="center" show-overflow-tooltip v-if="isColumnVisible('remark')" />
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleDetail(scope.row)"
            >详情</el-button>
            <el-button
              size="mini"
              type="warning"
              @click="handleCallback(scope.row)"
              :disabled="scope.row.callback_status === '1' || scope.row.callback_status === 1"
            >回调</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="listQuery.page"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="listQuery.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          small
          class="compact-pagination"
        />
      </div>
    </el-card>

    <!-- 备注弹窗 -->
    <el-dialog title="修改备注" :visible.sync="remarkDialogVisible" width="30%">
      <el-form :model="remarkForm" label-width="80px">
        <el-form-item label="备注">
          <el-input v-model="remarkForm.remark" type="textarea" :rows="4" placeholder="请输入备注内容" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="remarkDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitRemark">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 详情弹窗 -->
    <el-dialog title="订单详情" :visible.sync="detailDialogVisible" width="65%" custom-class="order-detail-dialog" :close-on-click-modal="false">
      <div class="order-detail-container">
        <!-- 订单头部信息 -->
        <div class="order-detail-header">
          <div class="order-id-section">
            <div class="order-id-title">
              <i class="el-icon-document"></i> 订单信息
            </div>
            <div class="order-id-content">
              <div class="order-id-item">
                <span class="label">商户单号：</span>
                <span class="value copy-value">
                  {{ currentOrder.out_trade_no }}
                  <el-tooltip content="复制单号" placement="top" effect="light">
                    <i class="el-icon-document-copy copy-icon" @click="copyToClipboard(currentOrder.out_trade_no)"></i>
                  </el-tooltip>
                </span>
              </div>
              <div class="order-id-item">
                <span class="label">支付单号：</span>
                <span class="value copy-value">
                  {{ currentOrder.dy_order_id || '暂无' }}
                  <el-tooltip content="复制单号" placement="top" effect="light" v-if="currentOrder.dy_order_id">
                    <i class="el-icon-document-copy copy-icon" @click="copyToClipboard(currentOrder.dy_order_id)"></i>
                  </el-tooltip>
                </span>
              </div>
            </div>
          </div>
          <div class="order-status-section">
            <div class="status-item">
              <div class="status-label">支付状态</div>
              <el-tag size="medium" :type="currentOrder.pay_status | payStatusFilter" effect="dark">
                <i :class="currentOrder.pay_status == 1 ? 'el-icon-check' : 'el-icon-time'"></i>
                {{ currentOrder.pay_status | payStatusText }}
              </el-tag>
            </div>
            <div class="status-item">
              <div class="status-label">回调状态</div>
              <el-tag size="medium" :type="currentOrder.callback_status | callbackStatusFilter" effect="dark">
                <i :class="currentOrder.callback_status == 1 ? 'el-icon-check' : 'el-icon-time'"></i>
                {{ currentOrder.callback_status | callbackStatusText }}
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 主要信息卡片 -->
        <div class="detail-cards-container">
          <!-- 支付信息卡片 -->
          <el-card class="detail-card payment-card" shadow="hover">
            <div slot="header" class="card-header">
              <i class="el-icon-money"></i> 支付信息
            </div>
            <div class="card-content">
              <div class="detail-item">
                <span class="detail-label">收款账号</span>
                <span class="detail-value">{{ currentOrder.account_name }}</span>
              </div>
              <div class="detail-item amount-item">
                <span class="detail-label">收款金额</span>
                <span class="detail-value highlight">¥{{ currentOrder.amount }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">通道编码</span>
                <span class="detail-value">
                  <el-tag size="medium" effect="plain" class="centered-tag">{{ currentOrder.paytype }}</el-tag>
                </span>
              </div>
            </div>
          </el-card>

          <!-- 时间信息卡片 -->
          <el-card class="detail-card time-card" shadow="hover">
            <div slot="header" class="card-header">
              <i class="el-icon-time"></i> 时间信息
            </div>
            <div class="card-content">
              <div class="detail-item">
                <span class="detail-label">创建时间</span>
                <span class="detail-value time-value">
                  <i class="el-icon-date"></i>
                  {{ currentOrder.create_time | formatTimestamp }}
                </span>
              </div>
              <div class="detail-item">
                <span class="detail-label">回调时间</span>
                <span class="detail-value time-value">
                  <i class="el-icon-date"></i>
                  {{ currentOrder.callback_time | formatTimestamp }}
                </span>
              </div>
            </div>
          </el-card>

          <!-- 归属信息卡片 -->
          <el-card class="detail-card owner-card" shadow="hover">
            <div slot="header" class="card-header">
              <i class="el-icon-user"></i> 归属信息
            </div>
            <div class="card-content">
              <div class="detail-item">
                <span class="detail-label">下单商户</span>
                <span class="detail-value">{{ currentOrder.merchant_name }}</span>
              </div>
              <div class="detail-item">
                <span class="detail-label">核销</span>
                <span class="detail-value">{{ currentOrder.user_nickname || '暂无' }}</span>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 备注信息卡片 -->
        <el-card class="remark-card" shadow="hover">
          <div slot="header" class="card-header">
            <i class="el-icon-edit-outline"></i> 备注信息
          </div>
          <div class="remark-content">
            <span v-if="currentOrder.remark">{{ currentOrder.remark }}</span>
            <span v-else class="no-remark">暂无备注信息</span>
          </div>
        </el-card>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关 闭</el-button>
        <el-button
          type="warning"
          @click="handleCallback(currentOrder)"
          :disabled="currentOrder.callback_status === '1' || currentOrder.callback_status === 1"
        >
          <i class="el-icon-refresh"></i> 回调
        </el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getOrderList, updateOrderRemark, callbackOrder, getOrderDetail } from '@/api/order'
import { exportOrder } from '@/api/order'

export default {
  name: 'OrderManagement',
  filters: {
    payStatusFilter(status) {
      const statusMap = {
        0: 'info',
        1: 'success'
      }
      return statusMap[status]
    },
    payStatusText(status) {
      const statusMap = {
        0: '未支付',
        1: '已支付'
      }
      return statusMap[status]
    },
    callbackStatusFilter(status) {
      // 处理字符串类型的状态值
      const statusValue = typeof status === 'string' ? parseInt(status) : status
      const statusMap = {
        0: 'info',
        1: 'success'
      }
      return statusMap[statusValue]
    },
    callbackStatusText(status) {
      // 处理字符串类型的状态值
      const statusValue = typeof status === 'string' ? parseInt(status) : status
      const statusMap = {
        0: '未回调',
        1: '已回调'
      }
      return statusMap[statusValue]
    },
    // 格式化时间戳为日期时间字符串
    formatTimestamp(timestamp) {
      if (!timestamp || timestamp === '') return '暂无'

      // 处理字符串类型的时间戳
      const ts = typeof timestamp === 'string' ? parseInt(timestamp) : timestamp

      // 检查是否为有效数字
      if (isNaN(ts)) return '无效时间'

      // 检查是否为秒级时间戳（10位数）或毫秒级时间戳（13位数）
      const date = ts.toString().length === 10
        ? new Date(ts * 1000)
        : new Date(ts)

      if (isNaN(date.getTime())) return '无效时间'

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  },
  data() {
    return {
      list: [],
      total: 0,
      listLoading: true,
      searchLoading: false,
      refreshLoading: false,
      exportLoading: false,
      searchTimer: null,
      listQuery: {
        page: 1,
        limit: 10,
        out_trade_no: '',
        dy_order_id: '',
        pay_status: '',
        callback_status: '',
        paytype: '',
        merchant_name: '',
        amount_min: '',
        amount_max: '',
        start_time: '',
        end_time: ''
      },
      dateRange: [],
      pickerOptions: {
        shortcuts: [{
          text: '最近一小时',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '今天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setHours(0, 0, 0, 0)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '昨天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24)
            start.setHours(0, 0, 0, 0)
            end.setTime(end.getTime() - 3600 * 1000 * 24)
            end.setHours(23, 59, 59, 999)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最近一周',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      payStatusOptions: [
        { value: '0', label: '未支付' },
        { value: '1', label: '已支付' }
      ],
      callbackStatusOptions: [
        { value: '0', label: '未回调' },
        { value: '1', label: '已回调' }
      ],
      remarkDialogVisible: false,
      detailDialogVisible: false,
      remarkForm: {
        id: '',
        remark: ''
      },
      currentOrder: {},
      multipleSelection: [],
      checkAll: false,
      checkedColumns: [],
      columnOptions: [
        { prop: 'out_trade_no', label: '商户单号' },
        { prop: 'dy_order_id', label: '支付单号' },
        { prop: 'account_name', label: '收款账号' },
        { prop: 'amount', label: '收款金额' },
        { prop: 'paytype', label: '收款类型' },
        { prop: 'pay_status', label: '支付状态' },
        { prop: 'callback_status', label: '回调状态' },
        { prop: 'callback_time', label: '回调时间' },
        { prop: 'create_time', label: '创建时间' },
        { prop: 'merchant_name', label: '下单商户' },
        { prop: 'user_nickname', label: '所属用户' },
        { prop: 'remark', label: '备注' }
      ]
    }
  },
  watch: {
    dateRange(val) {
      if (val) {
        this.listQuery.start_time = val[0]
        this.listQuery.end_time = val[1]
      } else {
        this.listQuery.start_time = ''
        this.listQuery.end_time = ''
      }
    }
  },
  created() {
    // 初始化列设置
    this.initColumnSettings()
    // 恢复上次的查询条件
    this.restoreQueryState()
    this.fetchData()
  },
  methods: {
    initColumnSettings() {
      // 默认显示所有列
      this.checkedColumns = this.columnOptions.map(col => col.prop)
      this.checkAll = true
    },
    // 保存查询状态到本地存储
    saveQueryState() {
      const queryState = {
        listQuery: this.listQuery,
        dateRange: this.dateRange
      }
      localStorage.setItem('orderQueryState', JSON.stringify(queryState))
    },
    // 从本地存储恢复查询状态
    restoreQueryState() {
      try {
        const savedState = localStorage.getItem('orderQueryState')
        if (savedState) {
          const queryState = JSON.parse(savedState)

          // 清除旧版本可能存在的字段
          if (queryState.listQuery) {
            // 删除旧字段
            delete queryState.listQuery.merchantOrderNo
            delete queryState.listQuery.orderNo
            delete queryState.listQuery.payStatus
            delete queryState.listQuery.callbackStatus
            delete queryState.listQuery.startTime
            delete queryState.listQuery.endTime
            delete queryState.listQuery.dy_order_id

            // 确保新字段存在
            queryState.listQuery.out_trade_no = queryState.listQuery.out_trade_no || ''
            queryState.listQuery.dy_order_id = queryState.listQuery.dy_order_id || ''
            queryState.listQuery.pay_status = queryState.listQuery.pay_status || ''
            queryState.listQuery.callback_status = queryState.listQuery.callback_status || ''
            queryState.listQuery.paytype = queryState.listQuery.paytype || ''
            queryState.listQuery.merchant_name = queryState.listQuery.merchant_name || ''
            queryState.listQuery.amount_min = queryState.listQuery.amount_min || ''
            queryState.listQuery.amount_max = queryState.listQuery.amount_max || ''
            queryState.listQuery.start_time = queryState.listQuery.start_time || ''
            queryState.listQuery.end_time = queryState.listQuery.end_time || ''
          }

          this.listQuery = queryState.listQuery || this.listQuery
          this.dateRange = queryState.dateRange || this.dateRange
        }
      } catch (e) {
        // 出错时清除本地存储
        localStorage.removeItem('orderQueryState')
      }
    },
    fetchData() {
      this.listLoading = true

      return getOrderList(this.listQuery).then(response => {
        // 处理后端返回的特定结构: { data: { list: [], total: number } }
        if (response.data && response.data.list && Array.isArray(response.data.list)) {
          this.list = response.data.list
          this.total = response.data.total || 0
        } else {
          this.list = []
          this.total = 0
        }

        this.listLoading = false
        return Promise.resolve()
      }).catch(error => {
        this.listLoading = false
        this.$message.error(error.message || '获取订单列表失败')
        return Promise.reject(error)
      })
    },
    handleSearch() {
      this.listQuery.page = 1
      this.searchLoading = true
      this.fetchData().finally(() => {
        this.searchLoading = false
        // 保存查询状态
        this.saveQueryState()
      })
    },
    resetQuery() {
      this.listQuery = {
        page: 1,
        limit: 10,
        out_trade_no: '',
        dy_order_id: '',
        pay_status: '',
        callback_status: '',
        paytype: '',
        merchant_name: '',
        amount_min: '',
        amount_max: '',
        start_time: '',
        end_time: ''
      }
      this.dateRange = []
      this.searchLoading = true
      this.fetchData().finally(() => {
        this.searchLoading = false
        // 清除本地存储的查询状态
        localStorage.removeItem('orderQueryState')
      })
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.fetchData()
    },
    handleDetail(row) {
      this.listLoading = true
      getOrderDetail(row.id).then(response => {
        // 处理后端返回的特定结构: { data: {} }
        if (response.data && typeof response.data === 'object') {
          this.currentOrder = response.data
          this.detailDialogVisible = true
        } else {
          this.$message.error('获取订单详情失败: 响应格式错误')
        }

        this.listLoading = false
      }).catch(error => {
        this.listLoading = false
        this.$message.error(error.message || '获取订单详情失败')
      })
    },
    handleCallback(row) {
      const callbackStatus = typeof row.callback_status === 'string' ? parseInt(row.callback_status) : row.callback_status

      if (callbackStatus === 1) {
        this.$message({
          type: 'warning',
          message: '该订单已成功回调，无需再次操作'
        })
        return
      }

      this.$confirm(`确定要对订单 ${row.out_trade_no} 进行回调操作吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.submitCallback(row)
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消回调操作'
        })
      })
    },
    submitCallback(row) {
      this.listLoading = true

      callbackOrder(row.id).then(response => {
        this.$message({
          type: 'success',
          message: '回调操作成功!'
        })
        this.fetchData()
      }).catch(error => {
        this.$message({
          type: 'error',
          message: error.message || '回调操作失败!'
        })
        this.listLoading = false
      })
    },
    formatDateTime(date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    handleRemark(row) {
      this.remarkForm.id = row.id
      this.remarkForm.remark = row.remark
      this.remarkDialogVisible = true
    },
    submitRemark() {
      this.listLoading = true

      updateOrderRemark(this.remarkForm).then(response => {
        this.$message({
          type: 'success',
          message: '备注修改成功!'
        })
        this.remarkDialogVisible = false
        this.fetchData()
      }).catch(error => {
        this.$message({
          type: 'error',
          message: error.message || '备注修改失败!'
        })
        this.listLoading = false
      })
    },
    debounceSearch() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer)
      }
      this.searchTimer = setTimeout(() => {
        this.handleSearch()
      }, 500)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleCheckAllChange(val) {
      this.checkedColumns = val ? this.columnOptions.map(col => col.prop) : []
    },
    handleCheckedColumnsChange(val) {
      this.checkedColumns = val
    },
    isColumnVisible(prop) {
      return this.checkedColumns.includes(prop)
    },
    tableRowClassName({ row }) {
      const callbackStatus = typeof row.callback_status === 'string' ? parseInt(row.callback_status) : row.callback_status

      if (row.pay_status === 0) {
        return 'warning-row'
      } else if (row.pay_status === 1 && callbackStatus === 0) {
        return 'info-row'
      } else if (row.pay_status === 1 && callbackStatus === 1) {
        return 'success-row'
      }
      return ''
    },
    handleRefresh() {
      this.refreshLoading = true
      this.fetchData().finally(() => {
        this.refreshLoading = false
        this.$message({
          type: 'success',
          message: '数据已刷新',
          duration: 1500
        })
      })
    },
    setQuickDateRange(type) {
      const end = new Date()
      const start = new Date()

      switch (type) {
        case 'hour':
          start.setTime(start.getTime() - 3600 * 1000)
          break
        case 'today':
          start.setHours(0, 0, 0, 0)
          break
        case 'yesterday':
          start.setTime(start.getTime() - 3600 * 1000 * 24)
          start.setHours(0, 0, 0, 0)
          end.setTime(end.getTime() - 3600 * 1000 * 24)
          end.setHours(23, 59, 59, 999)
          break
        case 'week':
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
          break
      }

      this.dateRange = [
        this.formatDateTime(start),
        this.formatDateTime(end)
      ]

      // 触发搜索
      this.handleSearch()
    },
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
          this.$message({
            message: '复制成功',
            type: 'success',
            duration: 1500
          })
        }).catch(() => {
          this.$message.error('复制失败，请手动复制')
        })
      } else {
        // 兼容性处理
        const textarea = document.createElement('textarea')
        textarea.value = text
        document.body.appendChild(textarea)
        textarea.select()
        try {
          document.execCommand('copy')
          this.$message({
            message: '复制成功',
            type: 'success',
            duration: 1500
          })
        } catch (err) {
          this.$message.error('复制失败，请手动复制')
        }
        document.body.removeChild(textarea)
      }
    },
    async handleExport() {
      this.exportLoading = true
      try {
        const params = { ...this.listQuery }
        delete params.page
        delete params.limit
        const res = await exportOrder(params)
        if (res.data && res.data.download_url) {
          window.open(res.data.download_url, '_blank')
          this.$message.success('导出任务已生成，正在下载...')
        } else {
          this.$message.error('导出失败，未获取到下载链接')
        }
      } catch (e) {
        this.$message.error(e.message || '导出失败')
      }
      this.exportLoading = false
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-container {
  margin-bottom: 20px;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.order-no-search {
  display: flex;
  align-items: center;
}

// 表格行高亮样式
::v-deep .el-table .warning-row {
  background-color: rgba(230, 162, 60, 0.1);
}
::v-deep .el-table .info-row {
  background-color: rgba(144, 147, 153, 0.1);
}
::v-deep .el-table .success-row {
  background-color: rgba(103, 194, 58, 0.1);
}

// 日期过滤器样式
.date-filter-container {
  display: flex;
  flex-direction: column;

  .quick-date-filters {
    margin-top: 5px;
    display: flex;
    flex-wrap: wrap;

    .el-button {
      margin-right: 8px;
      margin-bottom: 5px;
      padding: 3px 6px;
    }
  }
}

// 金额区间搜索样式
.amount-range-container {
  display: flex;
  align-items: center;

  .range-separator {
    margin: 0 8px;
    color: #909399;
    font-size: 12px;
  }
}

// 紧凑表格样式
.compact-table {
  font-size: 12px;

  // 调整表头样式
  ::v-deep .el-table__header th {
    padding: 8px 0;
    background-color: #f5f7fa;
  }

  // 调整单元格样式
  ::v-deep .el-table__body td {
    padding: 4px 0;
    height: auto;
    line-height: normal;
  }

  // 设置表格行高
  ::v-deep .el-table__row {
    height: 40px;
  }

  // 订单号列样式
  .order-no-column {
    padding: 0 5px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;

    .order-no-item {
      line-height: 1.2;
      margin: 1px 0;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .label {
        color: #909399;
        font-size: 11px;
      }
      .value {
        font-size: 12px;
        margin-left: 2px;
      }
    }
  }

  // 金额样式
  .amount-value {
    font-weight: bold;
    color: #f56c6c;
  }

  // 时间列样式
  ::v-deep .time-column {
    font-size: 11px;
    color: #606266;
  }

  // 标签样式
  ::v-deep .el-tag {
    padding: 0 5px;
    height: 20px;
    line-height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  // 按钮样式
  ::v-deep .el-button--mini {
    padding: 5px 8px;
    font-size: 11px;
  }
}

// 订单详情样式
.order-detail-dialog {
  ::v-deep .el-dialog__header {
    padding: 12px 15px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }

  ::v-deep .el-dialog__title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
  }

  ::v-deep .el-dialog__body {
    padding: 15px;
  }

  ::v-deep .el-dialog__footer {
    padding: 12px 15px;
    border-top: 1px solid #ebeef5;
    background-color: #f5f7fa;
  }
}

.order-detail-container {
  padding: 0;
}

.order-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f9fafc;
  border-radius: 6px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.05);
}

.order-id-section {
  flex: 1;
}

.order-id-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 12px;
  color: #303133;

  i {
    margin-right: 5px;
    color: #409EFF;
  }
}

.order-id-content {
  .order-id-item {
    margin-bottom: 8px;

    .label {
      font-weight: bold;
      color: #606266;
    }

    .value {
      margin-left: 5px;
      color: #303133;
      font-family: monospace;

      &.copy-value {
        display: flex;
        align-items: center;

        .copy-icon {
          margin-left: 8px;
          color: #909399;
          cursor: pointer;
          font-size: 14px;
          transition: color 0.3s;

          &:hover {
            color: #409EFF;
          }
        }
      }
    }
  }
}

.order-status-section {
  display: flex;

  .status-item {
    margin-left: 20px;
    text-align: center;

    .status-label {
      font-weight: bold;
      margin-bottom: 8px;
      color: #606266;
    }

    .el-tag {
      padding: 6px 12px;
      font-size: 13px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 80px;

      i {
        margin-right: 5px;
      }
    }
  }
}

.detail-cards-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px 15px;
}

.detail-card {
  flex: 1;
  min-width: 220px;
  margin: 0 8px 15px;
  transition: transform 0.3s;

  &:hover {
    transform: translateY(-3px);
  }

  .card-header {
    font-weight: bold;
    color: #303133;
    font-size: 14px;

    i {
      margin-right: 5px;
    }
  }

  .card-content {
    padding: 8px 0;
  }
}

.payment-card {
  ::v-deep .el-card__header {
    background-color: rgba(64, 158, 255, 0.1);
    padding: 10px 15px;

    i {
      color: #409EFF;
    }
  }
}

.time-card {
  ::v-deep .el-card__header {
    background-color: rgba(144, 147, 153, 0.1);
    padding: 10px 15px;

    i {
      color: #909399;
    }
  }

  .time-value {
    i {
      margin-right: 5px;
      color: #909399;
    }
  }
}

.owner-card {
  ::v-deep .el-card__header {
    background-color: rgba(103, 194, 58, 0.1);
    padding: 10px 15px;

    i {
      color: #67c23a;
    }
  }
}

.detail-item {
  margin-bottom: 12px;
  padding: 0 15px;

  &:last-child {
    margin-bottom: 0;
  }

  .detail-label {
    font-weight: bold;
    color: #606266;
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
  }

  .detail-value {
    color: #303133;
    font-size: 13px;

    &.highlight {
      color: #f56c6c;
      font-weight: bold;
      font-size: 16px;
    }

    .centered-tag {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      min-width: 60px;
    }
  }
}

.amount-item {
  .detail-value {
    font-size: 18px;
  }
}

.remark-card {
  margin-bottom: 15px;

  ::v-deep .el-card__header {
    background-color: rgba(230, 162, 60, 0.1);
    padding: 10px 15px;

    i {
      color: #e6a23c;
    }
  }

  .remark-content {
    padding: 12px 15px;
    min-height: 50px;
    white-space: pre-wrap;
    color: #303133;
    line-height: 1.5;
    background-color: #fafafa;
    border-radius: 4px;
    font-size: 13px;

    .no-remark {
      color: #909399;
      font-style: italic;
    }
  }
}

// 紧凑分页样式
.compact-pagination {
  ::v-deep .el-pagination__total {
    margin-left: 10px;
  }
  ::v-deep .el-pagination__sizes {
    margin-right: 10px;
  }
  ::v-deep .el-pagination__jump {
    margin-right: 10px;
  }
}

// 列设置弹窗样式
.column-filter-popover {
  .el-checkbox-group {
    margin-bottom: 10px;
  }
  .el-checkbox {
    margin-right: 10px;
  }
}
</style>
