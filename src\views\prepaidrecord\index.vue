<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="filter-container">
        <div class="filter-row">
          <el-input
            v-model="listQuery.merchant_id"
            placeholder="商户ID"
            class="filter-item search-input"
            @keyup.enter.native="handleFilter"
          />
          <el-select
            v-model="listQuery.change_type"
            placeholder="变更类型"
            clearable
            class="filter-item status-select"
          >
            <el-option label="增加" value="increase" />
            <el-option label="减少" value="decrease" />
          </el-select>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            class="filter-item date-picker"
            @change="handleDateChange"
          />
          <el-input
            v-model="listQuery.remark"
            placeholder="备注"
            class="filter-item search-input"
            @keyup.enter.native="handleFilter"
          />
          <el-button
            class="filter-item"
            type="primary"
            icon="el-icon-search"
            @click="handleFilter"
          >
            搜索
          </el-button>
          <el-button
            class="filter-item"
            icon="el-icon-refresh"
            @click="handleReset"
          >
            重置
          </el-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-scroll-x">
        <el-table
          v-loading="loading"
          :data="tableData"
          border
          stripe
          highlight-current-row
          class="prepaid-record-table"
          :style="{ minWidth: '1200px' }"
        >
          <el-table-column prop="id" label="记录ID" width="80" align="center" />
          <el-table-column prop="merchant_id" label="商户ID" width="100" align="center" />
          <el-table-column prop="merchant_name" label="商户名称" min-width="120" align="center" />
          <el-table-column label="变更前金额" width="120" align="center">
            <template slot-scope="scope">
              <span class="amount-text">{{ scope.row.before_amount | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column label="变更后金额" width="120" align="center">
            <template slot-scope="scope">
              <span class="amount-text">{{ scope.row.after_amount | formatMoney }}</span>
            </template>
          </el-table-column>
          <el-table-column label="变更金额" width="120" align="center">
            <template slot-scope="scope">
              <span
                :class="['amount-text', scope.row.change_type === '增加' ? 'amount-increase' : 'amount-decrease']"
              >
                {{ scope.row.change_type === '增加' ? '+' : '-' }}{{ scope.row.change_amount_formatted }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="change_type" label="变更类型" width="100" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.change_type === '增加' ? 'success' : 'danger'">
                {{ scope.row.change_type }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150" align="center">
            <template slot-scope="scope">
              <el-input
                v-if="scope.row.editing"
                v-model="scope.row.tempRemark"
                size="mini"
                @blur="handleRemarkBlur(scope.row)"
                @keyup.enter.native="handleRemarkBlur(scope.row)"
              />
              <div v-else class="remark-cell">
                <span class="remark-text">{{ scope.row.remark || '-' }}</span>
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  size="mini"
                  @click="handleEditRemark(scope.row)"
                />
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createtime_text" label="创建时间" width="160" align="center" />
        </el-table>
      </div>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="listQuery.page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="listQuery.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { getPrepaidRecordList, updatePrepaidRecordRemark } from '@/api/prepaidrecord'

export default {
  name: 'PrepaidRecord',
  filters: {
    formatMoney(value) {
      if (!value && value !== 0) return '0.00'
      return parseFloat(value).toFixed(2)
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      dateRange: [],
      listQuery: {
        page: 1,
        limit: 10,
        merchant_id: '',
        change_type: '',
        start_time: '',
        end_time: '',
        remark: ''
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 获取列表数据
    async getList() {
      this.loading = true
      try {
        const response = await getPrepaidRecordList(this.listQuery)
        if (response.code === 1) {
          this.tableData = response.data.list.map(item => ({
            ...item,
            editing: false,
            tempRemark: item.remark
          }))
          this.total = response.data.total
        } else {
          this.$message.error(response.msg || '获取预付记录失败')
        }
      } catch (error) {
        console.error('获取预付记录失败:', error)
        this.$message.error('获取预付记录失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },

    // 重置搜索条件
    handleReset() {
      this.listQuery = {
        page: 1,
        limit: 10,
        merchant_id: '',
        change_type: '',
        start_time: '',
        end_time: '',
        remark: ''
      }
      this.dateRange = []
      this.getList()
    },

    // 日期范围变化
    handleDateChange(dates) {
      if (dates && dates.length === 2) {
        this.listQuery.start_time = dates[0]
        this.listQuery.end_time = dates[1]
      } else {
        this.listQuery.start_time = ''
        this.listQuery.end_time = ''
      }
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.listQuery.page = 1
      this.getList()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },

    // 开始编辑备注
    handleEditRemark(row) {
      row.editing = true
      row.tempRemark = row.remark || ''
      this.$nextTick(() => {
        // 聚焦到输入框
        const input = this.$el.querySelector('.el-input__inner')
        if (input) {
          input.focus()
        }
      })
    },

    // 备注编辑完成
    async handleRemarkBlur(row) {
      if (row.tempRemark === row.remark) {
        row.editing = false
        return
      }

      try {
        const response = await updatePrepaidRecordRemark({
          id: row.id,
          remark: row.tempRemark
        })

        if (response.code === 1) {
          row.remark = row.tempRemark
          row.editing = false
          this.$message.success('修改备注成功')
        } else {
          row.tempRemark = row.remark
          row.editing = false
          this.$message.error(response.msg || '修改备注失败')
        }
      } catch (error) {
        console.error('修改备注失败:', error)
        row.tempRemark = row.remark
        row.editing = false
        this.$message.error('修改备注失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;

  .filter-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
  }

  .filter-item {
    margin-bottom: 10px;
  }

  .search-input {
    width: 200px;
  }

  .status-select {
    width: 150px;
  }

  .date-picker {
    width: 300px;
  }
}

.table-scroll-x {
  overflow-x: auto;
  margin-bottom: 20px;
}

.prepaid-record-table {
  .amount-text {
    font-weight: 500;
  }

  .amount-increase {
    color: #67c23a;
  }

  .amount-decrease {
    color: #f56c6c;
  }

  .remark-cell {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .remark-text {
      flex: 1;
      text-align: left;
    }
  }
}

.pagination-container {
  text-align: right;
  margin-top: 20px;
}
</style>
