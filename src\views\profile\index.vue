<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="8">
        <el-card class="box-card user-info-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span class="card-title"><i class="el-icon-user"></i> 个人资料</span>
          </div>
          <div class="text-center avatar-container">
            <div class="avatar-wrapper">
              <!-- 使用与导航栏相同的Logo -->
              <svg class="user-avatar" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
                <!-- 盾牌主体 -->
                <path d="M512 64L896 192v288c0 259.2-172.8 428.8-384 480-211.2-51.2-384-220.8-384-480V192L512 64z" fill="#1890ff"/>

                <!-- 渐变定义 -->
                <defs>
                  <linearGradient id="metallic" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.6"/>
                    <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0"/>
                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0.2"/>
                  </linearGradient>
                  <linearGradient id="centerGlow" x1="50%" y1="0%" x2="50%" y2="100%">
                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.8"/>
                    <stop offset="50%" style="stop-color:#ffffff;stop-opacity:0.3"/>
                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0"/>
                  </linearGradient>
                  <radialGradient id="innerShine" cx="50%" cy="40%" r="60%">
                    <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.6"/>
                    <stop offset="100%" style="stop-color:#ffffff;stop-opacity:0"/>
                  </radialGradient>
                </defs>

                <!-- 金属光泽效果 -->
                <path d="M512 64L896 192v288c0 259.2-172.8 428.8-384 480-211.2-51.2-384-220.8-384-480V192L512 64z" fill="url(#metallic)"/>

                <!-- 内部装饰线条 -->
                <path d="M512 128L832 230.4v249.6c0 224-149.3 371.2-320 416-170.7-44.8-320-192-320-416V230.4L512 128z" fill="none" stroke="#ffffff" stroke-width="2" opacity="0.3"/>

                <!-- 放射状光晕 -->
                <circle cx="512" cy="384" r="160" fill="url(#innerShine)" opacity="0.4"/>

                <!-- 中心装饰图案 -->
                <path d="M512 320l128 96-48 160-80 48-80-48-48-160 128-96z" fill="#ffffff" opacity="0.15"/>
                <path d="M512 352l96 64-32 128-64 32-64-32-32-128 96-64z" fill="#ffffff" opacity="0.3"/>
                <path d="M512 384l64 32-16 96-48 16-48-16-16-96 64-32z" fill="url(#centerGlow)"/>

                <!-- 装饰性圆环 -->
                <circle cx="512" cy="448" r="96" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.2"/>
                <circle cx="512" cy="448" r="64" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>

                <!-- 高光效果 -->
                <path d="M512 96L832 208v48L512 144 192 256v-48L512 96z" fill="#ffffff" opacity="0.4"/>

                <!-- 边缘高光 -->
                <path d="M512 64L896 192 512 320 128 192 512 64z" fill="none" stroke="#ffffff" stroke-width="4" opacity="0.5"/>

                <!-- 中心点缀 -->
                <circle cx="512" cy="448" r="8" fill="#ffffff" opacity="0.8"/>
                <circle cx="512" cy="448" r="4" fill="#ffffff"/>

                <!-- 四角装饰 -->
                <path d="M472 408l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
                <path d="M552 408l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
                <path d="M472 488l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
                <path d="M552 488l-16-16 16-16 16 16-16 16z" fill="#ffffff" opacity="0.4"/>
              </svg>
            </div>
            <h3 class="username">{{ name }}</h3>
            <p class="user-role">
              <el-tag size="small" type="success">{{ username }}</el-tag>
            </p>
          </div>
          <el-divider content-position="center">账户信息</el-divider>
          <div class="user-stats">
            <div class="stat-item">
              <div class="stat-icon">
                <el-badge :value="money > -1 ? 'VIP' : ''" class="money-badge">
                  <i class="el-icon-wallet"></i>
                </el-badge>
              </div>
              <div class="stat-content">
                <div class="stat-label">账户余额</div>
                <div class="stat-value money">¥ {{ formatMoney(money) }}</div>
              </div>
            </div>
            <el-divider></el-divider>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="stat-content">
                <div class="stat-label">最后登录时间</div>
                <div class="stat-value">{{ formatDate(logintime) }}</div>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <i class="el-icon-location-information"></i>
              </div>
              <div class="stat-content">
                <div class="stat-label">登录IP</div>
                <div class="stat-value">{{ loginip }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :span="16">
        <el-card class="box-card" shadow="hover">
          <div slot="header" class="clearfix">
            <span class="card-title"><i class="el-icon-edit"></i> 修改昵称</span>
          </div>
          <el-form ref="nicknameForm" :model="nicknameForm" :rules="nicknameRules" label-width="100px" class="profile-form">
            <el-form-item label="新昵称" prop="nickname">
              <el-input v-model="nicknameForm.nickname" placeholder="请输入新昵称">
                <template slot="prefix">
                  <i class="el-icon-user"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-check" @click="handleUpdateNickname">保存修改</el-button>
            </el-form-item>
          </el-form>
        </el-card>

        <el-card class="box-card" shadow="hover" style="margin-top: 20px;">
          <div slot="header" class="clearfix">
            <span class="card-title"><i class="el-icon-lock"></i> 修改密码</span>
          </div>
          <el-form ref="passwordForm" :model="passwordForm" :rules="passwordRules" label-width="100px" class="profile-form">
            <el-form-item label="原密码" prop="oldPassword">
              <el-input v-model="passwordForm.oldPassword" type="password" show-password placeholder="请输入原密码">
                <template slot="prefix">
                  <i class="el-icon-key"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="新密码" prop="newPassword">
              <el-input v-model="passwordForm.newPassword" type="password" show-password placeholder="请输入新密码">
                <template slot="prefix">
                  <i class="el-icon-lock"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="确认密码" prop="confirmPassword">
              <el-input v-model="passwordForm.confirmPassword" type="password" show-password placeholder="请再次输入新密码">
                <template slot="prefix">
                  <i class="el-icon-lock"></i>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-check" @click="handleUpdatePassword">修改密码</el-button>
              <el-button icon="el-icon-refresh-right" @click="resetPasswordForm">重置</el-button>
            </el-form-item>
          </el-form>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { updateProfile } from '@/api/user'

export default {
  name: 'Profile',
  data() {
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    return {
      userInfo: {
        username: '',
        nickname: '',
        money: 0,
        logintime: '',
        loginip: ''
      },
      nicknameForm: {
        nickname: ''
      },
      nicknameRules: {
        nickname: [
          { required: true, message: '请输入昵称', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ]
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'name',
      'username',
      'loginip',
      'logintime',
      'money'
    ])
  },
  methods: {
    handleUpdateNickname() {
      this.$refs.nicknameForm.validate(valid => {
        if (valid) {
          updateProfile({ nickname: this.nicknameForm.nickname }).then(response => {
            this.$message.success('昵称修改成功')
            this.$store.commit('user/SET_NAME', this.nicknameForm.nickname)
            this.userInfo.nickname = this.nicknameForm.nickname
            this.nicknameForm.nickname = ''
          }).catch(error => {
            this.$message.error('昵称修改失败：' + error.message)
          })
        }
      })
    },
    handleUpdatePassword() {
      this.$refs.passwordForm.validate(valid => {
        if (valid) {
          updateProfile({
            oldPassword: this.passwordForm.oldPassword,
            newPassword: this.passwordForm.newPassword
          }).then(response => {
            this.$message.success('密码修改成功')
            this.resetPasswordForm()
            // 刷新用户信息
            this.$store.dispatch('user/getInfo')
          })
        }
      })
    },
    resetPasswordForm() {
      this.$refs.passwordForm.resetFields()
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    },
    formatMoney(money) {
      return parseFloat(money).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatDate(date) {
      if (!date) return ''

      // 尝试处理时间戳（数字或字符串）
      if (typeof date === 'string' || typeof date === 'number') {
        const timestamp = parseInt(date)
        if (!isNaN(timestamp)) {
          // 检查时间戳长度，如果是秒级时间戳则转换为毫秒级
          date = new Date(timestamp.toString().length === 10 ? timestamp * 1000 : timestamp)
        } else if (typeof date === 'string') {
          // 处理日期字符串
          date = new Date(date.replace(/-/g, '/'))
        }
      }

      // 验证日期是否有效
      if (!(date instanceof Date) || isNaN(date)) {
        return ''
      }

      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;

  .box-card {
    margin-bottom: 20px;
    border-radius: 8px;
    overflow: hidden;

    .card-title {
      font-size: 16px;
      font-weight: 500;
      color: #303133;

      i {
        margin-right: 8px;
        font-size: 18px;
        vertical-align: -1px;
      }
    }
  }

  .user-info-card {
    .avatar-container {
      padding: 30px 0;
      background: linear-gradient(to bottom, #f5f7fa, #ffffff);

      .avatar-wrapper {
        position: relative;
        display: inline-block;

        .user-avatar {
          width: 100px;
          height: 100px;
          border: 2px solid #1890ff;
          border-radius: 50%;
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
          transition: all 0.3s ease;
          cursor: pointer;
          padding: 8px;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(24, 144, 255, 0.2);
          }
        }
      }

      .username {
        margin: 20px 0 10px;
        font-size: 22px;
        color: #303133;
        font-weight: 600;
      }

      .user-role {
        margin: 0;
      }
    }

    .user-stats {
      padding: 0 20px;

      .stat-item {
        display: flex;
        align-items: flex-start;
        padding: 16px 0;

        .stat-icon {
          width: 40px;
          height: 40px;
          background: #f5f7fa;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;

          i {
            font-size: 20px;
            color: #409EFF;
          }
        }

        .stat-content {
          flex: 1;

          .stat-label {
            color: #909399;
            font-size: 13px;
            margin-bottom: 4px;
          }

          .stat-value {
            color: #303133;
            font-weight: 500;
            font-size: 15px;

            &.money {
              color: #f56c6c;
              font-size: 18px;
              font-weight: 600;
            }
          }
        }
      }
    }

    .money-badge {
      ::v-deep .el-badge__content {
        background-color: #E6A23C;
      }
    }
  }

  .profile-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px 0;

    .el-input {
      .el-input__prefix {
        left: 10px;
        color: #909399;
      }

      input {
        padding-left: 35px;
      }
    }

    .el-button {
      padding: 12px 20px;

      & + .el-button {
        margin-left: 10px;
      }
    }
  }
}

.text-center {
  text-align: center;
}

::v-deep .el-divider__text {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  background-color: #fff;
  padding: 0 20px;
}
</style>
