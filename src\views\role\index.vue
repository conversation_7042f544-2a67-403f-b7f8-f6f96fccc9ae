<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.roleName"
          placeholder="角色名称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.roleKey"
          placeholder="角色标识"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          class="filter-item"
          style="width: 130px"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          />
        </el-select>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="success"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
        <el-button
          class="filter-item"
          type="warning"
          icon="el-icon-download"
          @click="handleExport"
        >
          导出
        </el-button>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        border
        stripe
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" width="80" label="序号" align="center" />
        <el-table-column prop="roleName" label="角色名称" min-width="120" align="center" />
        <el-table-column prop="roleKey" label="角色标识" min-width="120" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.roleKey === 'admin' ? 'danger' : 'primary'">
              {{ scope.row.roleKey }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="角色描述" min-width="200" align="center" show-overflow-tooltip />
        <el-table-column prop="status" label="状态" min-width="100" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'启用'"
              :inactive-value="'禁用'"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="180" align="center" />
        <el-table-column label="操作" min-width="250" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="success"
              icon="el-icon-setting"
              @click="handlePermission(scope.row)"
            >
              权限
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 角色编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form
        ref="dataForm"
        :model="temp"
        label-position="right"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model="temp.roleName" placeholder="请输入角色名称" />
        </el-form-item>
        <el-form-item label="角色标识" prop="roleKey">
          <el-input v-model="temp.roleKey" placeholder="请输入角色标识" />
        </el-form-item>
        <el-form-item label="角色描述" prop="description">
          <el-input
            v-model="temp.description"
            type="textarea"
            :rows="3"
            placeholder="请输入角色描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio label="启用">启用</el-radio>
            <el-radio label="禁用">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveData">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog title="分配权限" :visible.sync="permissionDialogVisible" width="600px">
      <el-tree
        ref="permissionTree"
        :data="permissionData"
        show-checkbox
        node-key="id"
        :default-checked-keys="checkedPermissions"
        :props="defaultProps"
      >
        <span class="custom-tree-node" slot-scope="{ node, data }">
          <span>
            <i :class="data.icon || 'el-icon-folder'" style="margin-right: 8px"></i>
            {{ node.label }}
          </span>
        </span>
      </el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="savePermissions">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Role',
  data() {
    return {
      listQuery: {
        roleName: '',
        roleKey: '',
        status: ''
      },
      statusOptions: [
        { key: '启用', label: '启用' },
        { key: '禁用', label: '禁用' }
      ],
      tableData: [
        {
          roleName: '超级管理员',
          roleKey: 'admin',
          description: '系统最高权限，可以操作所有功能',
          status: '启用',
          createTime: '2024-01-20 10:00:00'
        },
        {
          roleName: '编辑员',
          roleKey: 'editor',
          description: '可以编辑内容，但无法进行系统设置',
          status: '启用',
          createTime: '2024-01-20 11:00:00'
        }
      ],
      currentPage: 1,
      pageSize: 10,
      total: 2,
      dialogVisible: false,
      dialogTitle: '',
      temp: {
        roleName: '',
        roleKey: '',
        description: '',
        status: '启用'
      },
      rules: {
        roleName: [{ required: true, message: '请输入角色名称', trigger: 'blur' }],
        roleKey: [{ required: true, message: '请输入角色标识', trigger: 'blur' }],
        description: [{ required: true, message: '请输入角色描述', trigger: 'blur' }]
      },
      permissionDialogVisible: false,
      permissionData: [
        {
          id: 1,
          label: '系统管理',
          icon: 'el-icon-setting',
          children: [
            {
              id: 11,
              label: '用户管理',
              icon: 'el-icon-user'
            },
            {
              id: 12,
              label: '角色管理',
              icon: 'el-icon-s-custom'
            }
          ]
        },
        {
          id: 2,
          label: '内容管理',
          icon: 'el-icon-document',
          children: [
            {
              id: 21,
              label: '文章管理',
              icon: 'el-icon-notebook-2'
            },
            {
              id: 22,
              label: '评论管理',
              icon: 'el-icon-chat-line-square'
            }
          ]
        }
      ],
      checkedPermissions: [11, 21],
      defaultProps: {
        children: 'children',
        label: 'label'
      }
    }
  },
  methods: {
    handleFilter() {
      this.$message.success('触发了搜索')
      // 这里添加搜索逻辑
    },
    handleAdd() {
      this.dialogTitle = '新增角色'
      this.temp = {
        roleName: '',
        roleKey: '',
        description: '',
        status: '启用'
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleEdit(row) {
      this.dialogTitle = '编辑角色'
      this.temp = Object.assign({}, row) // copy obj
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleDelete(row) {
      this.$confirm('确认删除该角色吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功：' + row.roleName)
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },
    handleStatusChange(row) {
      this.$message.success(`角色 ${row.roleName} 状态已更改为 ${row.status}`)
    },
    handlePermission(row) {
      this.permissionDialogVisible = true
      // 这里应该根据角色ID获取已分配的权限
      this.$nextTick(() => {
        this.$refs.permissionTree.setCheckedKeys(this.checkedPermissions)
      })
    },
    handleExport() {
      this.$message.success('触发了导出功能')
      // 这里添加导出逻辑
    },
    handleSizeChange(val) {
      this.pageSize = val
      // 这里添加获取数据的逻辑
    },
    handleCurrentChange(val) {
      this.currentPage = val
      // 这里添加获取数据的逻辑
    },
    saveData() {
      this.$refs['dataForm'].validate((valid) => {
        if (valid) {
          if (this.dialogTitle === '新增角色') {
            this.$message.success('新增成功')
          } else {
            this.$message.success('编辑成功')
          }
          this.dialogVisible = false
        }
      })
    },
    savePermissions() {
      const checkedKeys = this.$refs.permissionTree.getCheckedKeys()
      const halfCheckedKeys = this.$refs.permissionTree.getHalfCheckedKeys()
      const allCheckedKeys = [...checkedKeys, ...halfCheckedKeys]
      this.$message.success('权限保存成功：' + allCheckedKeys.join(','))
      this.permissionDialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  padding-bottom: 20px;
  .filter-item {
    margin-right: 10px;
    vertical-align: middle;
    &:last-child {
      margin-right: 0;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-table th {
  background-color: #f5f7fa;
}

::v-deep .el-tag {
  text-transform: capitalize;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
}
</style>
