<template>
  <div class="app-container">
    <el-card class="box-card">
      <!-- 搜索区域 -->
      <div class="filter-container">
        <el-input
          v-model="listQuery.username"
          placeholder="用户名"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-input
          v-model="listQuery.nickname"
          placeholder="昵称"
          style="width: 200px;"
          class="filter-item"
          @keyup.enter.native="handleFilter"
        />
        <el-select
          v-model="listQuery.status"
          placeholder="状态"
          clearable
          class="filter-item"
          style="width: 130px"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.key"
            :label="item.label"
            :value="item.key"
          />
        </el-select>
        <el-button
          class="filter-item"
          type="primary"
          icon="el-icon-search"
          @click="handleFilter"
        >
          搜索
        </el-button>
        <el-button
          class="filter-item"
          style="margin-left: 10px;"
          type="success"
          icon="el-icon-plus"
          @click="handleAdd"
        >
          新增
        </el-button>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        stripe
        highlight-current-row
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="id" label="用户ID" width="80" align="center" />
        <el-table-column prop="username" label="用户名" min-width="120" align="center" />
        <el-table-column prop="nickname" label="昵称" min-width="120" align="center" />
        <el-table-column prop="money" label="余额" min-width="120" align="center">
          <template slot-scope="scope">
            <span :class="['money-value', scope.row.money >= 0 ? 'money-positive' : 'money-negative']">
              {{ scope.row.money | formatMoney }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="收款金额" align="center">
          <el-table-column prop="todayAmount" label="今日收款" width="90" align="center">
            <template slot-scope="scope">
              <span class="today-amount">{{ formatAmount(scope.row.todayAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="yesterdayAmount" label="昨日收款" width="90" align="center">
            <template slot-scope="scope">
              <span class="amount-text">{{ formatAmount(scope.row.yesterdayAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="totalAmount" label="总收款" width="90" align="center">
            <template slot-scope="scope">
              <span class="amount-text">{{ formatAmount(scope.row.totalAmount) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="status" label="状态" min-width="100" align="center">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="'normal'"
              :inactive-value="'hidden'"
              @change="handleStatusChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="createtime" label="登录时间" min-width="180" align="center">
          <template slot-scope="scope">
            {{ formatTimestamp(scope.row.createtime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="200" align="center">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit"
              class="operation-btn"
              @click="handleEdit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              size="mini"
              type="warning"
              icon="el-icon-money"
              class="operation-btn"
              @click="handleBalance(scope.row)"
            >
              余额
            </el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              class="operation-btn"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-card>

    <!-- 用户编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px">
      <el-form
        ref="dataForm"
        :model="temp"
        label-position="right"
        label-width="100px"
        :rules="rules"
      >
        <el-form-item label="用户名" v-if="dialogTitle === '新增用户'" prop="username">
          <el-input v-model="temp.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="temp.nickname" placeholder="请输入昵称" />
        </el-form-item>
        <template v-if="dialogTitle === '新增用户'">
          <el-form-item label="密码" prop="password">
            <el-input v-model="temp.password" type="password" placeholder="请输入密码" />
          </el-form-item>
        </template>
        <template v-else>
          <el-form-item label="修改密码">
            <el-switch v-model="changePassword" />
          </el-form-item>
          <template v-if="changePassword">
            <el-form-item label="密码" prop="password">
              <el-input v-model="temp.password" type="password" placeholder="请输入新密码" />
            </el-form-item>
          </template>
        </template>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="temp.status">
            <el-radio label="normal">启用</el-radio>
            <el-radio label="hidden">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveData">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 余额调整对话框 -->
    <el-dialog title="增减余额" :visible.sync="balanceDialogVisible" width="400px">
      <el-form
        ref="balanceForm"
        :model="balanceForm"
        label-position="right"
        label-width="100px"
        :rules="balanceRules"
      >
        <el-form-item label="当前余额">
          <span>{{ balanceForm.currentMoney | formatMoney }}</span>
        </el-form-item>
        <el-form-item label="操作类型" prop="type">
          <el-radio-group v-model="balanceForm.type">
            <el-radio label="add">增加</el-radio>
            <el-radio label="subtract">减少</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="变更金额" prop="amount">
          <el-input-number
            v-model="balanceForm.amount"
            :min="0"
            :precision="2"
            :step="10"
            style="width: 200px;"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="balanceForm.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="balanceDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="saveBalance">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getUserList, updateUserStatus, deleteUser, addUser, updateUser, updateBalance } from '@/api/user'

export default {
  name: 'User',
  filters: {
    formatMoney(value) {
      if (!value) return '0.00'
      return `¥${value}` // 直接使用后端返回的格式化金额
    },
    formatStatus(status) {
      const statusMap = {
        normal: '启用',
        hidden: '禁用'
      }
      return statusMap[status] || status
    }
  },
  data() {
    return {
      listQuery: {
        username: '',
        nickname: '',
        role: '',
        status: '',
        page: 1,
        limit: 10
      },
      roleOptions: [
        { key: 'admin', label: '管理员' },
        { key: 'editor', label: '编辑员' }
      ],
      statusOptions: [
        { key: 'normal', label: '启用' },
        { key: 'hidden', label: '禁用' }
      ],
      tableData: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      dialogVisible: false,
      dialogTitle: '',
      temp: {
        username: '',
        nickname: '',
        password: '',
        status: 'normal'
      },
      changePassword: false,
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        nickname: [{ required: true, message: '请输入昵称', trigger: 'blur' }],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少为6个字符', trigger: 'blur' }
        ],
        status: [{ required: true, message: '请选择状态', trigger: 'change' }]
      },
      balanceDialogVisible: false,
      balanceForm: {
        userId: null,
        username: '',
        currentMoney: 0,
        type: 'add',
        amount: 0,
        remark: ''
      },
      balanceRules: {
        type: [{ required: true, message: '请选择操作类型', trigger: 'change' }],
        amount: [{ required: true, message: '请输入变更金额', trigger: 'blur' }],
        remark: [{ required: true, message: '请输入备注信息', trigger: 'blur' }]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getStatusLabel(status) {
      const statusMap = {
        normal: '启用',
        hidden: '禁用'
      }
      return statusMap[status] || status
    },
    async getList() {
      this.loading = true
      try {
        getUserList(this.listQuery).then(response => {
          const { list, total, page, limit } = response.data
          this.tableData = list.map(item => ({
            ...item,
            statusLabel: this.getStatusLabel(item.status),
            loginTime: this.formatTimestamp(item.createtime)
          }))
          this.total = total
          this.currentPage = page
          this.pageSize = limit
        }).finally(() => {
          this.loading = false
        })
      } catch (error) {
        this.$message.error('获取数据失败')
        console.error('Error:', error)
      }
    },
    formatTimestamp(timestamp) {
      if (!timestamp) return '-'
      // 如果时间戳是秒级的，需要转换为毫秒级
      const date = new Date(timestamp.toString().length === 10 ? timestamp * 1000 : timestamp)
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      const hours = date.getHours().toString().padStart(2, '0')
      const minutes = date.getMinutes().toString().padStart(2, '0')
      const seconds = date.getSeconds().toString().padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    handleFilter() {
      this.listQuery.page = 1
      this.getList()
    },
    handleAdd() {
      this.dialogTitle = '新增用户'
      this.changePassword = false
      this.temp = {
        username: '',
        nickname: '',
        password: '',
        status: 'normal'
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    handleEdit(row) {
      this.dialogTitle = '编辑用户'
      this.changePassword = false
      this.temp = {
        id: row.id,
        username: row.username,
        nickname: row.nickname,
        password: '',
        status: row.status
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].clearValidate()
      })
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确认删除该用户吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        deleteUser({ id: row.id }).then(response => {
          this.$message.success('删除成功')
          this.getList()
        })
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
          console.error('Error:', error)
        }
      }
    },
    async handleStatusChange(row) {
      updateUserStatus({
        id: row.id,
        status: row.status
      }).then(response => {
        this.$message.success(`用户 ${row.username} 状态已更新`)
      })
    },
    handleExport() {
      this.$message.success('触发了导出功能')
      // 这里添加导出逻辑
    },
    handleSizeChange(val) {
      this.listQuery.limit = val
      this.getList()
    },
    handleCurrentChange(val) {
      this.listQuery.page = val
      this.getList()
    },
    async saveData() {
      this.$refs['dataForm'].validate(async(valid) => {
        if (valid) {
          try {
            const isNew = this.dialogTitle === '新增用户'
            const apiMethod = isNew ? addUser : updateUser

            // 处理编辑时的数据
            const submitData = { ...this.temp }
            if (!isNew && !this.changePassword) {
              // 如果是编辑模式且没有修改密码，删除密码字段
              delete submitData.password
            }

            const response = await apiMethod(submitData)

            if (response.code === 1) {
              this.$message.success(isNew ? '新增成功' : '编辑成功')
              this.dialogVisible = false
              this.getList()
            } else {
              this.$message.error(response.msg || (isNew ? '新增失败' : '编辑失败'))
            }
          } catch (error) {
            this.$message.error(this.dialogTitle === '新增用户' ? '新增失败' : '编辑失败')
            console.error('Error:', error)
          }
        }
      })
    },
    handleBalance(row) {
      this.balanceDialogVisible = true
      this.balanceForm = {
        userId: row.id,
        username: row.username,
        currentMoney: row.money || 0,
        type: 'add',
        amount: 0,
        remark: ''
      }
      this.$nextTick(() => {
        this.$refs['balanceForm']?.clearValidate()
      })
    },
    async saveBalance() {
      this.$refs['balanceForm'].validate(async(valid) => {
        if (valid) {
          try {
            const { userId, type, amount, remark } = this.balanceForm
            // 这里调用更新余额接口
            const response = await updateBalance({
              id: userId,
              amount: type === 'add' ? amount : -amount,
              remark
            })

            if (response.code === 1) {
              this.$message.success('余额调整成功')
              this.balanceDialogVisible = false
              this.getList() // 刷新列表
            } else {
              this.$message.error(response.msg || '余额调整失败')
            }
          } catch (error) {
            this.$message.error('余额调整失败')
            console.error('Error:', error)
          }
        }
      })
    },
    formatAmount(amount) {
      if (amount === undefined || amount === null) return '¥0.00'
      // 确保 amount 是数字类型
      const numAmount = parseFloat(amount)
      if (isNaN(numAmount)) return '¥0.00'
      return `¥${numAmount.toFixed(2)}`
    }
  }
}
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.filter-container {
  padding-bottom: 20px;
  .filter-item {
    margin-right: 10px;
    vertical-align: middle;
    &:last-child {
      margin-right: 0;
    }
  }
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  text-align: right;
}

::v-deep .el-table th {
  background-color: #f5f7fa;
}

::v-deep .el-tag {
  text-transform: capitalize;
}

.money-value {
  display: inline-block;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 14px;
  transition: all 0.3s;

  &.money-positive {
    color: #67C23A;
    background-color: rgba(103, 194, 58, 0.1);
  }

  &.money-negative {
    color: #F56C6C;
    background-color: rgba(245, 108, 108, 0.1);
  }

  &:hover {
    transform: scale(1.05);
  }
}
.operation-btn {
  padding: 4px 8px;
  margin: 0 2px;
  font-size: 12px;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  [class^="el-icon-"] {
    font-size: 12px;
    margin-right: 2px;
  }
}

.revenue-details {
  padding: 10px;

  .revenue-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .label {
      color: #606266;
      font-size: 14px;
    }

    .value {
      font-weight: bold;
      font-size: 14px;
    }

    &.total {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #EBEEF5;

      .label, .value {
        font-size: 16px;
        font-weight: bold;
      }
    }
  }
}

.revenue-summary {
  display: inline-flex;
  align-items: center;

  .today {
    color: #67C23A;
    font-weight: bold;
    margin-right: 4px;
  }

  .total {
    color: #909399;
    font-size: 12px;
  }
}

.today-amount {
  color: #409EFF;
  font-weight: bold;
  font-size: 13px;
}

.amount-text {
  color: #606266;
  font-size: 13px;
}
</style>
